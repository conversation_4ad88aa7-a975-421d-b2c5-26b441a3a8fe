# WordPress Integration Configuration
REACT_APP_WORDPRESS_URL=https://your-wordpress-site.com

# Firebase Configuration (if using Firebase)
REACT_APP_FIREBASE_API_KEY=your-firebase-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=1:123456789:web:abcdef123456

# Performance Monitoring
REACT_APP_ENABLE_PERFORMANCE_MONITORING=true
REACT_APP_PERFORMANCE_BUDGET_FPS=30

# Development Settings
REACT_APP_ENABLE_DEBUG=false
REACT_APP_ENABLE_3D_MODELS=true
