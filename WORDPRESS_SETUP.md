# WordPress Integration Setup Guide

This guide will help you set up WordPress integration for your portfolio website's admin panel.

## Prerequisites

1. A WordPress website with admin access
2. WordPress REST API enabled (default in WordPress 4.7+)
3. JWT Authentication plugin installed

## Step 1: Install WordPress JWT Authentication Plugin

### Option A: Using WP-CLI (Recommended)
```bash
wp plugin install jwt-authentication-for-wp-rest-api --activate
```

### Option B: Manual Installation
1. Download the JWT Authentication plugin from WordPress.org
2. Upload to your WordPress `/wp-content/plugins/` directory
3. Activate the plugin in WordPress admin

## Step 2: Configure JWT Authentication

Add the following to your WordPress `wp-config.php` file:

```php
// JWT Authentication Configuration
define('JWT_AUTH_SECRET_KEY', 'your-super-secret-key-here');
define('JWT_AUTH_CORS_ENABLE', true);

// Optional: Set token expiration (default is 7 days)
define('JWT_AUTH_EXPIRE', 60 * 60 * 24 * 7); // 7 days in seconds
```

## Step 3: Configure CORS (Cross-Origin Resource Sharing)

Add this to your WordPress `.htaccess` file or server configuration:

```apache
# Enable CORS for your portfolio domain
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "https://your-portfolio-domain.com"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>

# Handle preflight OPTIONS requests
RewriteEngine On
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]
```

## Step 4: Configure Your Portfolio Website

1. Copy `.env.example` to `.env`:
```bash
cp .env.example .env
```

2. Update your `.env` file:
```env
REACT_APP_WORDPRESS_URL=https://your-wordpress-site.com
```

## Step 5: Test the Connection

1. Start your development server:
```bash
npm run dev
```

2. Navigate to `/admin` in your portfolio
3. Go to the "WordPress" tab
4. Click "Test Connection" to verify the setup
5. Use your WordPress admin credentials to log in

## WordPress REST API Endpoints Used

The integration uses these WordPress REST API endpoints:

- **Authentication**: `/wp-json/jwt-auth/v1/token`
- **Token Validation**: `/wp-json/jwt-auth/v1/token/validate`
- **Posts**: `/wp-json/wp/v2/posts`
- **Users**: `/wp-json/wp/v2/users`
- **Media**: `/wp-json/wp/v2/media`

## Security Considerations

1. **Use HTTPS**: Always use HTTPS for both your WordPress site and portfolio
2. **Strong Secret Key**: Use a strong, unique JWT secret key
3. **CORS Configuration**: Only allow your portfolio domain in CORS settings
4. **User Permissions**: Ensure WordPress users have appropriate permissions
5. **Regular Updates**: Keep WordPress and plugins updated

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Verify CORS headers are properly configured
   - Check that your portfolio domain is whitelisted

2. **Authentication Failures**
   - Verify JWT plugin is installed and activated
   - Check that JWT_AUTH_SECRET_KEY is set in wp-config.php
   - Ensure WordPress user has proper permissions

3. **Connection Timeouts**
   - Check WordPress site is accessible
   - Verify REST API is enabled
   - Test endpoints manually with tools like Postman

### Testing Endpoints Manually

Test your WordPress REST API:

```bash
# Test basic connectivity
curl https://your-wordpress-site.com/wp-json/wp/v2/posts

# Test authentication
curl -X POST https://your-wordpress-site.com/wp-json/jwt-auth/v1/token \
  -H "Content-Type: application/json" \
  -d '{"username":"your-username","password":"your-password"}'
```

## Features Available

Once configured, you can:

- ✅ Authenticate with WordPress from your portfolio admin
- ✅ View and manage blog posts
- ✅ Create new blog posts
- ✅ Edit existing posts
- ✅ Delete posts
- ✅ Upload media files
- ✅ Manage post status (draft, published, private)

## Advanced Configuration

### Custom Post Types

To support custom post types, modify the WordPress service:

```typescript
// In src/services/wordpress.ts
async getCustomPosts(postType: string) {
  const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/${postType}`);
  return response.json();
}
```

### Custom Fields

For custom fields support, install the Advanced Custom Fields (ACF) plugin and use:

```typescript
// Access custom fields in post data
const post = await wordpressService.getPost(id);
console.log(post.acf); // Custom fields data
```

## Support

If you encounter issues:

1. Check the browser console for error messages
2. Verify WordPress error logs
3. Test API endpoints manually
4. Ensure all prerequisites are met

For additional help, refer to:
- [WordPress REST API Documentation](https://developer.wordpress.org/rest-api/)
- [JWT Authentication Plugin Documentation](https://wordpress.org/plugins/jwt-authentication-for-wp-rest-api/)
