
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON><PERSON> 🚀 | Developer | Cloud & DevOps Enthusiast</title>
    <meta name="description" content="Aditya <PERSON>vi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines." />
    <meta name="author" content="Adity<PERSON>" />
    <meta name="theme-color" content="#0f172a" />
    <meta name="color-scheme" content="dark light" />
    <meta name="keywords" content="developer, cloud, devops, portfolio, web development, software engineer" />
    <meta name="robots" content="index, follow" />

    <!-- Canonical URL -->
    <link rel="canonical" href="https://adityashenvi.com" />

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Open Graph / Facebook -->
    <meta property="og:title" content="Aditya Shenvi | Developer | Cloud & DevOps Enthusiast" />
    <meta property="og:description" content="Aditya Shenvi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://adityashenvi.com" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    <meta property="og:site_name" content="Aditya Shenvi Portfolio" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:creator" content="@lovable_dev" />
    <meta name="twitter:title" content="Aditya Shenvi | Developer | Cloud & DevOps Enthusiast" />
    <meta name="twitter:description" content="Aditya Shenvi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines." />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <!-- Preload Critical Assets -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- AMP Required Script -->
    <script async src="https://cdn.ampproject.org/v0.js"></script>

    <!-- AMP Boilerplate -->
    <style amp-boilerplate>body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}</style>
    <noscript><style amp-boilerplate>body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}</style></noscript>

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Aditya Shenvi",
        "url": "https://adityashenvi.com",
        "image": "https://lovable.dev/opengraph-image-p98pqg.png",
        "sameAs": [
          "https://github.com/adityashenvi",
          "https://twitter.com/lovable_dev",
          "https://linkedin.com/in/adityashenvi"
        ],
        "jobTitle": "Developer & Cloud/DevOps Enthusiast",
        "worksFor": {
          "@type": "Organization",
          "name": "Tech Company"
        }
      }
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Preload Critical Scripts -->
    <link rel="modulepreload" href="/src/main.tsx" />

    <!-- Inline Critical CSS -->
    <style>
      /* Critical CSS for initial render */
      body {
        margin: 0;
        padding: 0;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        background-color: #0f172a;
        color: #f8fafc;
      }
      #root {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
    </style>
  </body>
</html>
