/**
 * Firebase deployment script
 * This script automates the build and deployment process to Firebase
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  projectId: 'adityashenvi-portfoliowebsite',
  buildCommand: 'npm run build',
  deployCommand: 'firebase deploy',
  distDir: 'dist',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Helper function to log with colors
const log = {
  info: (msg) => console.log(`${colors.blue}${colors.bright}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}${colors.bright}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}${colors.bright}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}${colors.bright}[ERROR]${colors.reset} ${msg}`),
  step: (step, msg) => console.log(`${colors.cyan}${colors.bright}[STEP ${step}]${colors.reset} ${msg}`),
};

// Main deployment function
async function deploy() {
  try {
    log.info('Starting deployment process...');
    
    // Step 1: Check if Firebase CLI is installed
    log.step(1, 'Checking Firebase CLI installation...');
    try {
      execSync('firebase --version', { stdio: 'ignore' });
      log.success('Firebase CLI is installed.');
    } catch (error) {
      log.error('Firebase CLI is not installed. Please install it with: npm install -g firebase-tools');
      process.exit(1);
    }
    
    // Step 2: Check if user is logged in to Firebase
    log.step(2, 'Checking Firebase login status...');
    try {
      execSync('firebase projects:list', { stdio: 'ignore' });
      log.success('You are logged in to Firebase.');
    } catch (error) {
      log.error('You are not logged in to Firebase. Please login with: firebase login');
      process.exit(1);
    }
    
    // Step 3: Build the project
    log.step(3, 'Building the project...');
    try {
      log.info('Running build command: ' + config.buildCommand);
      execSync(config.buildCommand, { stdio: 'inherit' });
      log.success('Build completed successfully.');
    } catch (error) {
      log.error('Build failed. Please check the error messages above.');
      process.exit(1);
    }
    
    // Step 4: Check if dist directory exists
    log.step(4, 'Checking build output...');
    if (!fs.existsSync(config.distDir)) {
      log.error(`Build directory '${config.distDir}' does not exist. Build may have failed.`);
      process.exit(1);
    }
    log.success(`Build directory '${config.distDir}' exists.`);
    
    // Step 5: Deploy to Firebase
    log.step(5, 'Deploying to Firebase...');
    try {
      log.info('Running deploy command: ' + config.deployCommand);
      execSync(config.deployCommand, { stdio: 'inherit' });
      log.success('Deployment completed successfully!');
      
      // Get the hosting URL
      const output = execSync('firebase hosting:channel:list --json').toString();
      const channels = JSON.parse(output);
      const liveUrl = `https://${config.projectId}.web.app`;
      
      log.info('Your site is now live at:');
      console.log(`${colors.bright}${colors.green}${liveUrl}${colors.reset}`);
    } catch (error) {
      log.error('Deployment failed. Please check the error messages above.');
      process.exit(1);
    }
    
  } catch (error) {
    log.error('An unexpected error occurred:');
    console.error(error);
    process.exit(1);
  }
}

// Run the deployment
deploy();
