/**
 * <PERSON><PERSON><PERSON> to generate AMP-compatible versions of pages
 * This script is used during the build process to create AMP versions of pages
 */

import fs from 'fs';
import path from 'path';

// Simple CSS minification function
function createAmpStyles(css) {
  return css
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
    .replace(/\s+/g, ' ') // Collapse whitespace
    .replace(/;\s*}/g, '}') // Remove unnecessary semicolons
    .trim();
}

// Configuration
const config = {
  // Source directory for HTML files
  sourceDir: 'dist',
  // Output directory for AMP files
  outputDir: 'dist/amp',
  // Pages to convert to AMP
  pagesToConvert: [
    'index.html',
    'dashboard/index.html',
    'login/index.html',
    'terminal/index.html',
    'tools/index.html',
  ],
  // Maximum size for inline CSS (AMP limit is 75KB)
  maxInlineCssSize: 75000,
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

/**
 * Convert HTML to AMP-compatible HTML
 * @param {string} html - HTML content to convert
 * @returns {string} - AMP-compatible HTML
 */
function convertToAmp(html) {
  // Add AMP boilerplate
  let ampHtml = html
    // Add AMP attribute to html tag
    .replace('<html', '<html amp')
    // Remove non-AMP scripts
    .replace(/<script(?!\s+amp|\s+type="application\/ld\+json")[^>]*>[\s\S]*?<\/script>/g, '')
    // Remove non-AMP styles
    .replace(/<link rel="stylesheet"[^>]*>/g, '');

  // Extract and inline CSS
  const cssLinks = html.match(/<link rel="stylesheet"[^>]*href="([^"]+)"[^>]*>/g) || [];
  let inlineCss = '';

  cssLinks.forEach(link => {
    const cssPath = link.match(/href="([^"]+)"/)[1];
    const fullPath = path.join(config.sourceDir, cssPath);

    if (fs.existsSync(fullPath)) {
      const css = fs.readFileSync(fullPath, 'utf8');
      inlineCss += css;
    }
  });

  // Minify and limit CSS size
  const minifiedCss = createAmpStyles(inlineCss);
  const limitedCss = minifiedCss.substring(0, config.maxInlineCssSize);

  // Add inline CSS
  ampHtml = ampHtml.replace('</head>', `<style amp-custom>${limitedCss}</style></head>`);

  // Replace img tags with amp-img
  ampHtml = ampHtml.replace(/<img([^>]*)>/g, (match, attributes) => {
    const src = attributes.match(/src="([^"]*)"/)?.[1] || '';
    const alt = attributes.match(/alt="([^"]*)"/)?.[1] || '';
    const width = attributes.match(/width="([^"]*)"/)?.[1] || '640';
    const height = attributes.match(/height="([^"]*)"/)?.[1] || '320';

    return `<amp-img src="${src}" alt="${alt}" width="${width}" height="${height}" layout="responsive"></amp-img>`;
  });

  // Add AMP analytics if needed
  ampHtml = ampHtml.replace('</body>', `
    <amp-analytics type="gtag" data-credentials="include">
      <script type="application/json">
        {
          "vars": {
            "gtag_id": "UA-XXXXXXXX-X",
            "config": {
              "UA-XXXXXXXX-X": { "groups": "default" }
            }
          }
        }
      </script>
    </amp-analytics>
  </body>`);

  return ampHtml;
}

// Process each page
config.pagesToConvert.forEach(page => {
  const sourcePath = path.join(config.sourceDir, page);
  const outputPath = path.join(config.outputDir, page);

  // Create directory structure if needed
  const outputDir = path.dirname(outputPath);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  // Read source HTML
  if (fs.existsSync(sourcePath)) {
    const html = fs.readFileSync(sourcePath, 'utf8');

    // Convert to AMP
    const ampHtml = convertToAmp(html);

    // Write AMP HTML
    fs.writeFileSync(outputPath, ampHtml);
    console.log(`Generated AMP version: ${outputPath}`);
  } else {
    console.error(`Source file not found: ${sourcePath}`);
  }
});

console.log('AMP generation complete!');
