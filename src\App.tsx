
import { Suspense, lazy } from "react";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { NavbarProvider } from "@/hooks/use-navbar";
import { ThemeProvider } from "@/hooks/use-theme";
import { AuthProvider } from "@/hooks/use-auth";
import CustomCursor from "@/components/CustomCursor";

// Preload the Index page for immediate display
import Index from "./pages/Index";

// Lazy load other pages with prefetch for better performance
const Dashboard = lazy(() => import("./pages/Dashboard"));
const Login = lazy(() => import("./pages/Login"));
const AboutPage = lazy(() => import("./pages/AboutPage"));
const TerminalPage = lazy(() => import("./pages/TerminalPage"));
const ToolsPage = lazy(() => import("./pages/ToolsPage"));
const ProjectsPage = lazy(() => import("./pages/ProjectsPage"));
const AdminPage = lazy(() => import("./pages/AdminPage"));
const AdminPanel = lazy(() => import("./pages/AdminPanel"));
const TermsPage = lazy(() => import("./pages/TermsPage"));
const PrivacyPage = lazy(() => import("./pages/PrivacyPage"));
const NotFound = lazy(() => import("./pages/NotFound"));

// No need for LoadingSpinner import - using lightweight spinner

// Lightweight loading fallback component
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-[50vh]">
    <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
  </div>
);

// Configure React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <TooltipProvider>
        <AuthProvider>
          <NavbarProvider>
            <CustomCursor />
            <Sonner />
            <BrowserRouter>
              <Suspense fallback={<LoadingFallback />}>
                <Routes>
                  <Route path="/" element={<Index />} />
                  {/* AMP-specific route */}
                  <Route path="/amp/" element={<Index />} />
                  <Route path="/about" element={<AboutPage />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/terminal" element={<TerminalPage />} />
                  <Route path="/tools" element={<ToolsPage />} />
                  <Route path="/projects" element={<ProjectsPage />} />
                  <Route path="/admin" element={<AdminPage />} />
                  <Route path="/admin-panel" element={<AdminPanel />} />
                  <Route path="/terms" element={<TermsPage />} />
                  <Route path="/privacy" element={<PrivacyPage />} />
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Suspense>
            </BrowserRouter>
          </NavbarProvider>
        </AuthProvider>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
