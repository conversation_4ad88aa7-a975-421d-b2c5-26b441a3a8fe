
import { motion } from "framer-motion";
import { RotatingCube } from "@/components/RotatingCube";
import { Model3D } from "@/components/Model3D";
import { Linkedin, Github, Mail, Code, Cloud, Server, Database } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState, useEffect, useRef } from "react";
import { isAmpPage } from "@/utils/amp-utils";

export function About() {
  const [isAmp, setIsAmp] = useState(false);
  const [showModel, setShowModel] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Check if we're in AMP mode
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Intersection Observer for performance optimization
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stop observing once visible
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Toggle between 3D model and cube
  const toggleModel = () => {
    setShowModel(!showModel);
  };

  return (
    <section ref={sectionRef} id="about" className="py-20">
      <div className="container">
        <motion.div
          className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center"
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <div className="order-2 lg:order-1">
            <h2 className="text-3xl font-bold mb-6">
              <span className="text-gradient">About Me</span>
            </h2>

            <div className="space-y-4 text-muted-foreground">
              <p>
                Hello! I'm Aditya, a developer with a passion for cloud computing, DevOps, and creating tools that solve real problems.
                My journey into tech started during my first year of engineering when I built my first web application.
              </p>

              <p>
                Currently, I'm pursuing my degree in Computer Engineering, where I'm diving deep into systems design,
                cloud architecture, and honing my skills in both frontend and backend development.
              </p>

              <p>
                As a mid-level web developer with a strong focus on AWS cloud technologies, I specialize in building scalable
                infrastructure and CI/CD pipelines that streamline development workflows and reduce operational overhead.
              </p>

              <p>
                Beyond coding, I enjoy sharing my knowledge through technical blog posts,
                contributing to open source projects, and building developer tools that make everyday tasks easier.
              </p>
            </div>

            <div className="flex flex-wrap gap-3 mt-6">
              <Button variant="outline" size="sm" className="gap-2">
                <Linkedin className="h-4 w-4" />
                <a href="https://linkedin.com/in/adityashenvi" target="_blank" rel="noopener noreferrer">
                  LinkedIn
                </a>
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Github className="h-4 w-4" />
                <a href="https://github.com/adityashenvi" target="_blank" rel="noopener noreferrer">
                  GitHub
                </a>
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Mail className="h-4 w-4" />
                <a href="mailto:<EMAIL>">
                  Email
                </a>
              </Button>
              <Button variant="outline" size="sm" className="gap-2">
                <Code className="h-4 w-4" />
                <a href="#projects">
                  Projects
                </a>
              </Button>
            </div>
          </div>

          <div className="order-1 lg:order-2">
            <div className="relative">
              <div className="glass p-4 rounded-xl overflow-hidden shadow-xl">
                <div className="relative overflow-hidden rounded-lg h-64 bg-gradient-to-br from-primary/10 to-accent/10">
                  <img
                    src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Nnx8Y2xvdWQlMjBjb21wdXRpbmd8ZW58MHx8MHx8fDA%3D&auto=format&fit=crop&w=500&q=60"
                    alt="Aditya Shenvi"
                    className="absolute inset-0 w-full h-full object-cover opacity-20 mix-blend-overlay"
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    {showModel ? (
                      <Model3D className="h-full w-full" />
                    ) : (
                      <RotatingCube className="h-full w-full" />
                    )}
                  </div>
                  <div className="absolute bottom-4 left-4 z-10 glass p-2 rounded-lg bg-background/30 backdrop-blur-md border border-white/10">
                    <div className="text-sm font-medium">Aditya Shenvi</div>
                    <div className="text-xs text-muted-foreground">Developer & Cloud Enthusiast</div>
                  </div>

                  {/* Toggle button for 3D model */}
                  <button
                    onClick={toggleModel}
                    className="absolute top-4 right-4 z-10 glass p-2 rounded-full bg-background/30 backdrop-blur-md border border-white/10 hover:bg-primary/20 transition-colors"
                    aria-label={showModel ? "Show Cube" : "Show 3D Model"}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      {showModel ? (
                        <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                      ) : (
                        <path d="M12.89 1.45l8 4A2 2 0 0 1 22 7.24v9.53a2 2 0 0 1-1.11 1.79l-8 4a2 2 0 0 1-1.79 0l-8-4a2 2 0 0 1-1.1-1.8V7.24a2 2 0 0 1 1.11-1.79l8-4a2 2 0 0 1 1.78 0z"></path>
                      )}
                    </svg>
                  </button>
                </div>

                <div className="mt-4 grid grid-cols-3 gap-2">
                  <div className="glass bg-background/30 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary">3+</div>
                    <div className="text-xs text-muted-foreground">Years Coding</div>
                  </div>
                  <div className="glass bg-background/30 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary">15+</div>
                    <div className="text-xs text-muted-foreground">Projects</div>
                  </div>
                  <div className="glass bg-background/30 p-3 rounded-lg text-center">
                    <div className="text-2xl font-bold text-primary">5+</div>
                    <div className="text-xs text-muted-foreground">AWS Services</div>
                  </div>
                </div>
              </div>
              <div className="absolute -top-4 -right-4 bg-primary text-white text-sm font-medium py-1 px-3 rounded-full">
                Cloud & DevOps
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
