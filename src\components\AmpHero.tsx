import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>edin, Mail, Terminal, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { isAmpPage } from "@/utils/amp-utils";
import { useEffect, useState } from "react";

/**
 * AMP-compatible Hero component that doesn't use framer-motion animations
 * This provides a lightweight alternative for AMP pages
 */
export function AmpHero() {
  const [isAmp, setIsAmp] = useState(false);
  
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  const TextGradient = ({ children }: { children: React.ReactNode }) => (
    <span className="text-gradient">{children}</span>
  );

  return (
    <section id="home" className="min-h-screen pt-20 flex items-center relative overflow-hidden">
      <div className="container">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-6">
            <p className="text-primary font-medium mb-2 fade-in">
              Hi there, I'm
            </p>
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 fade-in-delay-1">
              <TextGradient>Aditya Shenvi</TextGradient>
            </h1>
            <div className="overflow-hidden h-12 md:h-16 fade-in-delay-2">
              <p className="text-xl md:text-2xl text-foreground/80 font-medium">
                Turning coffee <span className="inline-block">☕</span> into CI/CD pipelines <span className="inline-block">🚀</span>
              </p>
            </div>
            <p className="text-lg mt-2 text-muted-foreground fade-in-delay-3">
              3rd Year Engineering | Building tools that matter
            </p>
          </div>

          <div className="mb-8 max-w-2xl mx-auto fade-in-delay-4">
            <p className="text-muted-foreground">
              I'm a developer and cloud enthusiast specializing in creating exceptional digital experiences.
              Currently focused on building accessible, human-centered products with modern technologies.
            </p>
          </div>

          <div className="flex flex-wrap gap-4 justify-center mb-12 fade-in-delay-5">
            <Link to="/#projects">
              <Button className="bg-primary hover:bg-primary/90 group">
                View Projects
                <span className="ml-2">
                  <ArrowRight className="h-4 w-4" />
                </span>
              </Button>
            </Link>
            <Button variant="outline" className="group">
              Contact Me
              <span className="ml-2">
                <Mail className="h-4 w-4" />
              </span>
            </Button>
          </div>

          <div className="flex gap-4 justify-center fade-in-delay-6">
            <a 
              href="https://github.com" 
              target="_blank" 
              rel="noreferrer" 
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
            >
              <Github className="h-5 w-5" />
              <span className="sr-only">GitHub</span>
            </a>
            <a 
              href="https://linkedin.com" 
              target="_blank" 
              rel="noreferrer" 
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
            >
              <Linkedin className="h-5 w-5" />
              <span className="sr-only">LinkedIn</span>
            </a>
            <a 
              href="mailto:<EMAIL>" 
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
            >
              <Mail className="h-5 w-5" />
              <span className="sr-only">Email</span>
            </a>
            <a 
              href="/terminal" 
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
            >
              <Terminal className="h-5 w-5" />
              <span className="sr-only">Terminal Tools</span>
            </a>
          </div>
        </div>
      </div>
      
      <div className="absolute bottom-10 left-1/2 -translate-x-1/2 bounce">
        <a href="#about" className="text-muted-foreground hover:text-foreground transition-colors">
          <ArrowDown className="h-6 w-6" />
          <span className="sr-only">Scroll down</span>
        </a>
      </div>

      {/* AMP-specific styles */}
      {isAmp && (
        <style jsx>{`
          .fade-in {
            animation: fadeIn 0.5s ease-in-out forwards;
          }
          .fade-in-delay-1 {
            animation: fadeIn 0.5s ease-in-out 0.2s forwards;
            opacity: 0;
          }
          .fade-in-delay-2 {
            animation: fadeIn 0.5s ease-in-out 0.4s forwards;
            opacity: 0;
          }
          .fade-in-delay-3 {
            animation: fadeIn 0.5s ease-in-out 0.6s forwards;
            opacity: 0;
          }
          .fade-in-delay-4 {
            animation: fadeIn 0.5s ease-in-out 0.8s forwards;
            opacity: 0;
          }
          .fade-in-delay-5 {
            animation: fadeIn 0.5s ease-in-out 1s forwards;
            opacity: 0;
          }
          .fade-in-delay-6 {
            animation: fadeIn 0.5s ease-in-out 1.2s forwards;
            opacity: 0;
          }
          .bounce {
            animation: bounce 2s infinite;
          }
          @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
          }
          @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
          }
        `}</style>
      )}
    </section>
  );
}
