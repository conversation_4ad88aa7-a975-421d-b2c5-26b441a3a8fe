import React, { <PERSON>actNode, useEffect, useState } from 'react';

interface AmpWrapperProps {
  children: ReactNode;
}

/**
 * AmpWrapper component that conditionally renders content based on whether
 * the page is being viewed as an AMP page or a regular page.
 * 
 * This component helps with implementing AMP compatibility while maintaining
 * the full React experience for non-AMP users.
 */
const AmpWrapper: React.FC<AmpWrapperProps> = ({ children }) => {
  const [isAmp, setIsAmp] = useState(false);

  useEffect(() => {
    // Check if the page is being viewed as an AMP page
    const isAmpPage = window.location.search.includes('amp=1') || 
                      document.documentElement.hasAttribute('amp') ||
                      window.location.pathname.includes('/amp/');
    
    setIsAmp(isAmpPage);
    
    // Add AMP class to body if it's an AMP page
    if (isAmpPage) {
      document.body.classList.add('amp-mode');
    }
    
    return () => {
      document.body.classList.remove('amp-mode');
    };
  }, []);

  // If it's an AMP page, apply AMP-specific optimizations
  if (isAmp) {
    return (
      <div className="amp-compatible">
        {/* AMP-specific version of the content */}
        {children}
        
        {/* Add AMP-specific styles */}
        <style jsx>{`
          .amp-compatible {
            /* AMP-specific styles */
            max-width: 100vw;
            overflow-x: hidden;
          }
          
          /* Ensure images are responsive in AMP mode */
          .amp-compatible img {
            max-width: 100%;
            height: auto;
          }
          
          /* Simplify animations for AMP */
          .amp-compatible * {
            transition: all 0.3s ease;
            animation-duration: 0.3s !important;
          }
        `}</style>
      </div>
    );
  }

  // Regular React rendering for non-AMP pages
  return <>{children}</>;
};

export default AmpWrapper;
