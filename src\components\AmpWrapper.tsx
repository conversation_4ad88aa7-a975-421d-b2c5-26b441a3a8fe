import React, { ReactNode } from 'react';

interface AmpWrapperProps {
  children: ReactNode;
}

/**
 * Simplified AmpWrapper component that just passes through children
 * for better performance. AMP-specific optimizations are handled at build time.
 */
const AmpWrapper: React.FC<AmpWrapperProps> = ({ children }) => {
  // Just pass through children for better performance
  return <>{children}</>;
};

export default AmpWrapper;
