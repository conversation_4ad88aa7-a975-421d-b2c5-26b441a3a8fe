import { useRef, useState, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere, Box, Cylinder, Float, Text3D, Center } from '@react-three/drei';
import { Mesh, Group } from 'three';
import { useTheme } from '@/hooks/use-theme';
import { motion } from 'framer-motion';

interface AnimatedAvatarProps {
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  className?: string;
}

function TechyAvatar() {
  const groupRef = useRef<Group>(null);
  const { theme } = useTheme();
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.1;
    }
  });

  return (
    <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.2}>
      <group ref={groupRef}>
        {/* Head */}
        <Sphere args={[0.8, 32, 32]} position={[0, 0.5, 0]}>
          <meshStandardMaterial
            color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
            metalness={0.7}
            roughness={0.2}
            emissive={theme === 'dark' ? '#1a365d' : '#1e40af'}
            emissiveIntensity={0.2}
          />
        </Sphere>
        
        {/* Body */}
        <Cylinder args={[0.6, 0.8, 1.5, 8]} position={[0, -0.5, 0]}>
          <meshStandardMaterial
            color={theme === 'dark' ? '#4a90e2' : '#2563eb'}
            metalness={0.5}
            roughness={0.3}
          />
        </Cylinder>
        
        {/* Arms */}
        <Cylinder args={[0.2, 0.2, 1, 8]} position={[-1, 0, 0]} rotation={[0, 0, Math.PI / 4]}>
          <meshStandardMaterial
            color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
            metalness={0.6}
            roughness={0.2}
          />
        </Cylinder>
        
        <Cylinder args={[0.2, 0.2, 1, 8]} position={[1, 0, 0]} rotation={[0, 0, -Math.PI / 4]}>
          <meshStandardMaterial
            color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
            metalness={0.6}
            roughness={0.2}
          />
        </Cylinder>
        
        {/* Cloud symbol on chest */}
        <Box args={[0.3, 0.2, 0.1]} position={[0, 0.2, 0.6]}>
          <meshStandardMaterial
            color={theme === 'dark' ? '#ffffff' : '#000000'}
            emissive={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
            emissiveIntensity={0.3}
          />
        </Box>
        
        {/* Floating data particles */}
        {Array.from({ length: 8 }, (_, i) => (
          <Sphere
            key={i}
            args={[0.05, 8, 8]}
            position={[
              Math.sin(i * Math.PI / 4) * 2,
              Math.cos(i * Math.PI / 4) * 0.5,
              Math.cos(i * Math.PI / 4) * 2,
            ]}
          >
            <meshBasicMaterial
              color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
              transparent
              opacity={0.7}
            />
          </Sphere>
        ))}
      </group>
    </Float>
  );
}

function FloatingElements() {
  const { theme } = useTheme();
  
  return (
    <>
      {/* Floating code symbols */}
      <Float speed={2} rotationIntensity={0.5} floatIntensity={0.8}>
        <Text3D
          font="/fonts/helvetiker_regular.typeface.json"
          size={0.2}
          height={0.02}
          position={[-2, 1, 1]}
        >
          {'</>'}
          <meshStandardMaterial
            color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
            transparent
            opacity={0.8}
          />
        </Text3D>
      </Float>
      
      <Float speed={1.5} rotationIntensity={0.3} floatIntensity={0.6}>
        <Text3D
          font="/fonts/helvetiker_regular.typeface.json"
          size={0.15}
          height={0.02}
          position={[2, -1, 0.5]}
        >
          {'☁️'}
          <meshStandardMaterial
            color={theme === 'dark' ? '#4a90e2' : '#2563eb'}
            transparent
            opacity={0.7}
          />
        </Text3D>
      </Float>
    </>
  );
}

export function AnimatedAvatar({ 
  size = 'md', 
  interactive = true,
  className = ""
}: AnimatedAvatarProps) {
  const [hovered, setHovered] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { theme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  const sizeClasses = {
    sm: 'w-24 h-24',
    md: 'w-32 h-32',
    lg: 'w-48 h-48'
  };

  if (!mounted) {
    return (
      <div className={`${sizeClasses[size]} ${className} bg-gradient-to-br from-primary/20 to-accent/20 rounded-full flex items-center justify-center`}>
        <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
      </div>
    );
  }

  return (
    <motion.div 
      className={`${sizeClasses[size]} ${className} relative`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      onHoverStart={() => setHovered(true)}
      onHoverEnd={() => setHovered(false)}
    >
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <pointLight 
          position={[5, 5, 5]} 
          intensity={1.2} 
          color={theme === 'dark' ? '#58a6ff' : '#3b82f6'} 
        />
        <pointLight 
          position={[-5, -5, -5]} 
          intensity={0.6} 
          color={theme === 'dark' ? '#4a90e2' : '#2563eb'} 
        />
        
        {/* Main Avatar */}
        <TechyAvatar />
        
        {/* Floating Elements */}
        {size === 'lg' && <FloatingElements />}
        
        {/* Interactive Controls */}
        {interactive && (
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={true}
            autoRotateSpeed={hovered ? 3 : 1}
            maxPolarAngle={Math.PI / 1.5}
            minPolarAngle={Math.PI / 3}
          />
        )}
      </Canvas>
      
      {/* Glow effect */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 blur-xl -z-10 animate-pulse" />
    </motion.div>
  );
}

// Fallback 2D avatar for better performance
export function FallbackAvatar({ 
  size = 'md',
  className = ""
}: Pick<AnimatedAvatarProps, 'size' | 'className'>) {
  const { theme } = useTheme();
  
  const sizeClasses = {
    sm: 'w-24 h-24',
    md: 'w-32 h-32',
    lg: 'w-48 h-48'
  };

  return (
    <motion.div 
      className={`${sizeClasses[size]} ${className} relative`}
      whileHover={{ scale: 1.05 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <div className="w-full h-full rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center relative overflow-hidden">
        {/* Animated background */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 animate-pulse" />
        
        {/* Avatar content */}
        <div className="relative z-10 text-white text-2xl font-bold">
          AS
        </div>
        
        {/* Floating particles */}
        <div className="absolute top-2 right-2 w-2 h-2 bg-white/60 rounded-full animate-bounce" />
        <div className="absolute bottom-3 left-3 w-1.5 h-1.5 bg-white/40 rounded-full animate-pulse" />
        <div className="absolute top-1/2 left-1 w-1 h-1 bg-white/50 rounded-full animate-ping" />
      </div>
      
      {/* Glow effect */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary/30 to-accent/30 blur-lg -z-10" />
    </motion.div>
  );
}
