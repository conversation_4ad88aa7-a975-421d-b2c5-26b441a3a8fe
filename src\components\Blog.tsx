
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ArrowRight, Calendar, Clock, User } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { getBlogPosts } from "@/services/firebase";

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  author: string;
  createdAt: any;
  updatedAt: any;
  published: boolean;
  tags: string[];
  imageUrl?: string;
}

// Sample fallback blog posts data
const FALLBACK_BLOG_POSTS = [
  {
    id: "blog-1",
    title: "Optimizing Docker Containers for Production",
    excerpt: "Learn about best practices for optimizing Docker containers in production environments, including multi-stage builds and layer caching techniques.",
    author: "<PERSON><PERSON><PERSON>",
    createdAt: new Date("2025-04-10"),
    tags: ["Docker", "DevOps", "Performance"],
    imageUrl: "https://placehold.co/600x400/365CCE/FFFFFF?text=Docker+Optimization",
    published: true
  },
  {
    id: "blog-2",
    title: "Building Serverless Applications with AWS Lambda",
    excerpt: "A comprehensive guide to building scalable, cost-effective serverless applications using AWS Lambda, API Gateway, and DynamoDB.",
    author: "Aditya Shenvi",
    createdAt: new Date("2025-03-25"),
    tags: ["AWS", "Serverless", "Cloud"],
    imageUrl: "https://placehold.co/600x400/365CCE/FFFFFF?text=AWS+Lambda",
    published: true
  },
  {
    id: "blog-3",
    title: "CI/CD Pipeline Strategies for Kubernetes Deployments",
    excerpt: "Explore different CI/CD pipeline strategies for deploying applications to Kubernetes clusters, focusing on GitOps and ArgoCD.",
    author: "Aditya Shenvi",
    createdAt: new Date("2025-03-15"),
    tags: ["Kubernetes", "CI/CD", "GitOps"],
    imageUrl: "https://placehold.co/600x400/365CCE/FFFFFF?text=K8s+CI/CD",
    published: true
  }
];

// Sample latest updates
const LATEST_UPDATES = [
  "Deployed new CI/CD pipeline for SafeBite project",
  "Launched AI Terminal Tool with Gemini integration",
  "Published tutorial on Firebase Authentication",
  "Added Multi-Cloud Cost Comparison Tool to developer tools",
  "Completed AWS Solutions Architect Professional certification"
];

export function Blog() {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadPosts = async () => {
      try {
        const blogPosts = await getBlogPosts();
        const publishedPosts = blogPosts.filter((post: any) => post.published);
        setPosts(publishedPosts.length > 0 ? publishedPosts : FALLBACK_BLOG_POSTS);
      } catch (error) {
        console.error("Failed to load blog posts:", error);
        setPosts(FALLBACK_BLOG_POSTS);
      } finally {
        setLoading(false);
      }
    };

    loadPosts();
  }, []);

  const formatDate = (date: any) => {
    if (!date) return "Unknown date";

    if (date.toDate) {
      return date.toDate().toLocaleDateString();
    }

    if (date instanceof Date) {
      return date.toLocaleDateString();
    }

    return new Date(date).toLocaleDateString();
  };

  const calculateReadTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.split(' ').length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min read`;
  };

  return (
    <section id="blog" className="py-20">
      <div className="container">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="space-y-8"
        >
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-4">
              <span className="text-gradient">Blog & Updates</span>
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Insights and tutorials on cloud engineering, DevOps, and modern application development.
              Stay updated with my latest work and articles.
            </p>
          </div>

          {/* Latest Updates Marquee */}
          <div className="relative overflow-hidden bg-card/30 backdrop-blur-sm border border-white/10 rounded-lg py-3 px-4 mb-8">
            <div className="flex items-center gap-2 font-medium mb-2">
              <span className="text-primary">Latest Updates:</span>
            </div>
            <div className="overflow-hidden whitespace-nowrap">
              <div className="animate-[marquee_20s_linear_infinite] inline-block">
                {LATEST_UPDATES.map((update, index) => (
                  <span key={index} className="mx-6 inline-flex items-center">
                    <span className="h-1.5 w-1.5 bg-primary rounded-full mr-2"></span>
                    {update}
                  </span>
                ))}
              </div>
              <div className="animate-[marquee_20s_linear_infinite] inline-block">
                {LATEST_UPDATES.map((update, index) => (
                  <span key={index} className="mx-6 inline-flex items-center">
                    <span className="h-1.5 w-1.5 bg-primary rounded-full mr-2"></span>
                    {update}
                  </span>
                ))}
              </div>
            </div>
          </div>

          {/* Blog Posts */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-muted-foreground">Loading blog posts...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {posts.slice(0, 3).map((post) => (
                <motion.div
                  key={post.id}
                  className="glass overflow-hidden rounded-lg border border-white/10 transition-transform hover-scale"
                  whileHover={{ y: -5 }}
                  initial={{ opacity: 0 }}
                  whileInView={{ opacity: 1 }}
                  transition={{ duration: 0.5 }}
                  viewport={{ once: true }}
                >
                  <div className="relative h-48 overflow-hidden">
                    <img
                      src={post.imageUrl || "https://placehold.co/600x400/365CCE/FFFFFF?text=Blog+Post"}
                      alt={post.title}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
                    />
                  </div>

                  <div className="p-5">
                    <div className="flex gap-2 mb-2">
                      {post.tags.map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>

                    <h3 className="font-bold text-lg mb-2 line-clamp-2">{post.title}</h3>

                    <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <div className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {post.author}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {formatDate(post.createdAt)}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {calculateReadTime(post.content || post.excerpt)}
                        </div>
                      </div>

                      <Button variant="ghost" size="sm" className="text-primary">
                        Read more <ArrowRight className="ml-1 h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          <div className="flex justify-center mt-8">
            <Button>
              View All Posts <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
