import { useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere, MeshDistortMaterial, Float, Text3D, Center } from '@react-three/drei';
import { Mesh } from 'three';
import { useTheme } from '@/hooks/use-theme';

interface CloudModel3DProps {
  interactive?: boolean;
  autoRotate?: boolean;
  showText?: boolean;
  className?: string;
}

function CloudSphere() {
  const meshRef = useRef<Mesh>(null);
  const { theme } = useTheme();

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;
      meshRef.current.rotation.y += 0.01;
    }
  });

  return (
    <Float speed={2} rotationIntensity={0.5} floatIntensity={0.5}>
      <Sphere ref={meshRef} args={[1, 64, 64]} scale={1.2}>
        <MeshDistortMaterial
          color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
          attach="material"
          distort={0.4}
          speed={2}
          roughness={0.1}
          metalness={0.8}
          emissive={theme === 'dark' ? '#1a365d' : '#1e40af'}
          emissiveIntensity={0.2}
        />
      </Sphere>
    </Float>
  );
}

function CloudParticles() {
  const particlesRef = useRef<Mesh>(null);
  const { theme } = useTheme();

  useFrame((state) => {
    if (particlesRef.current) {
      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;
    }
  });

  const particles = Array.from({ length: 50 }, (_, i) => (
    <Sphere
      key={i}
      args={[0.02, 8, 8]}
      position={[
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 4,
        (Math.random() - 0.5) * 4,
      ]}
    >
      <meshBasicMaterial
        color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
        transparent
        opacity={0.6}
      />
    </Sphere>
  ));

  return <group ref={particlesRef}>{particles}</group>;
}

function CloudText() {
  const { theme } = useTheme();

  return (
    <Center>
      <Text3D
        font="/fonts/helvetiker_regular.typeface.json"
        size={0.3}
        height={0.05}
        curveSegments={12}
        bevelEnabled
        bevelThickness={0.02}
        bevelSize={0.01}
        bevelOffset={0}
        bevelSegments={5}
        position={[0, -2, 0]}
      >
        CLOUD
        <meshStandardMaterial
          color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
          metalness={0.8}
          roughness={0.2}
        />
      </Text3D>
    </Center>
  );
}

export function CloudModel3D({
  interactive = true,
  autoRotate = true,
  showText = false,
  className = ""
}: CloudModel3DProps) {
  const [hovered, setHovered] = useState(false);
  const { theme } = useTheme();

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 4], fov: 50 }}
        style={{ background: 'transparent' }}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
      >
        {/* Lighting */}
        <ambientLight intensity={0.4} />
        <pointLight
          position={[10, 10, 10]}
          intensity={1}
          color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
        />
        <pointLight
          position={[-10, -10, -10]}
          intensity={0.5}
          color={theme === 'dark' ? '#1a365d' : '#1e40af'}
        />

        {/* Main Cloud Sphere */}
        <CloudSphere />

        {/* Floating Particles */}
        <CloudParticles />

        {/* Optional Text */}
        {showText && (
          <Suspense fallback={null}>
            <CloudText />
          </Suspense>
        )}

        {/* Controls */}
        {interactive && (
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate={autoRotate}
            autoRotateSpeed={hovered ? 2 : 0.5}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 2}
          />
        )}
      </Canvas>
    </div>
  );
}

// Simplified version for performance-critical areas
export function SimpleCloudModel({ className = "" }: { className?: string }) {
  const { theme } = useTheme();

  return (
    <div className={`w-full h-full ${className}`}>
      <Canvas
        camera={{ position: [0, 0, 3], fov: 50 }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.6} />
        <pointLight position={[5, 5, 5]} intensity={0.8} />

        <Float speed={1} rotationIntensity={0.2} floatIntensity={0.3}>
          <Sphere args={[0.8, 32, 32]}>
            <meshStandardMaterial
              color={theme === 'dark' ? '#58a6ff' : '#3b82f6'}
              metalness={0.6}
              roughness={0.3}
              emissive={theme === 'dark' ? '#1a365d' : '#1e40af'}
              emissiveIntensity={0.1}
            />
          </Sphere>
        </Float>
      </Canvas>
    </div>
  );
}
