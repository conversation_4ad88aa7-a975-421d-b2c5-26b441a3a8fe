
import { useEffect, useState, useRef } from "react";
import { useTheme } from "@/hooks/use-theme";

// High-performance techy cursor with cloud engineer vibes
export default function CustomCursor() {
  const { theme } = useTheme();
  const [visible, setVisible] = useState(false);
  const [isPointer, setIsPointer] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const cursorRef = useRef<HTMLDivElement>(null);
  const trailRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Don't show custom cursor on mobile devices
    if ('ontouchstart' in window || window.innerWidth < 1024) {
      return;
    }

    // Create cursor elements
    const cursor = document.createElement('div');
    const trail = document.createElement('div');
    const particles = document.createElement('div');

    cursor.className = 'techy-cursor';
    trail.className = 'cursor-trail';
    particles.className = 'cursor-particles';

    document.body.appendChild(cursor);
    document.body.appendChild(trail);
    document.body.appendChild(particles);

    // Mouse position tracking with RAF for smooth performance
    let mouseX = 0;
    let mouseY = 0;
    let cursorX = 0;
    let cursorY = 0;
    let trailX = 0;
    let trailY = 0;

    const updateCursor = () => {
      // Smooth cursor movement with easing
      cursorX += (mouseX - cursorX) * 0.3;
      cursorY += (mouseY - cursorY) * 0.3;

      // Trail follows with more delay
      trailX += (mouseX - trailX) * 0.1;
      trailY += (mouseY - trailY) * 0.1;

      cursor.style.transform = `translate(${cursorX}px, ${cursorY}px)`;
      trail.style.transform = `translate(${trailX}px, ${trailY}px)`;

      requestAnimationFrame(updateCursor);
    };

    requestAnimationFrame(updateCursor);

    // Track mouse position
    const handleMouseMove = (e: MouseEvent) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
      setVisible(true);
    };

    // Check for interactive elements
    const checkInteractiveElements = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isClickable =
        target.tagName === 'A' ||
        target.tagName === 'BUTTON' ||
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.closest('a') ||
        target.closest('button') ||
        target.getAttribute('role') === 'button' ||
        target.classList.contains('cursor-pointer');

      const isTextInput = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA';

      setIsPointer(isClickable);
      setIsTyping(isTextInput);

      cursor.classList.toggle('cursor-hover', isClickable);
      cursor.classList.toggle('cursor-typing', isTextInput);
    };

    // Mouse events
    const handleMouseEnter = () => setVisible(true);
    const handleMouseLeave = () => setVisible(false);

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mousemove', checkInteractiveElements);
    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);

    // Add techy cursor styles
    const style = document.createElement('style');
    style.innerHTML = `
      body, * {
        cursor: none !important;
      }

      .techy-cursor {
        position: fixed;
        top: 0;
        left: 0;
        width: 20px;
        height: 20px;
        background: ${theme === 'dark' ? 'rgba(88, 166, 255, 0.8)' : 'rgba(59, 130, 246, 0.8)'};
        border: 2px solid ${theme === 'dark' ? '#58a6ff' : '#3b82f6'};
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        transform: translate(-50%, -50%);
        transition: all 0.15s ease-out;
        will-change: transform;
        box-shadow:
          0 0 20px ${theme === 'dark' ? 'rgba(88, 166, 255, 0.5)' : 'rgba(59, 130, 246, 0.5)'},
          inset 0 0 10px rgba(255, 255, 255, 0.2);
      }

      .cursor-trail {
        position: fixed;
        top: 0;
        left: 0;
        width: 40px;
        height: 40px;
        border: 1px solid ${theme === 'dark' ? 'rgba(88, 166, 255, 0.3)' : 'rgba(59, 130, 246, 0.3)'};
        border-radius: 50%;
        pointer-events: none;
        z-index: 9998;
        transform: translate(-50%, -50%);
        will-change: transform;
      }

      .techy-cursor.cursor-hover {
        width: 40px;
        height: 40px;
        background: transparent;
        border-width: 3px;
        box-shadow:
          0 0 30px ${theme === 'dark' ? 'rgba(88, 166, 255, 0.7)' : 'rgba(59, 130, 246, 0.7)'},
          inset 0 0 15px rgba(255, 255, 255, 0.1);
      }

      .techy-cursor.cursor-typing {
        width: 2px;
        height: 20px;
        border-radius: 2px;
        background: ${theme === 'dark' ? '#58a6ff' : '#3b82f6'};
        border: none;
        animation: blink 1s infinite;
      }

      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
      }

      .cursor-particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100px;
        height: 100px;
        pointer-events: none;
        z-index: 9997;
        transform: translate(-50%, -50%);
        background: radial-gradient(circle,
          ${theme === 'dark' ? 'rgba(88, 166, 255, 0.1)' : 'rgba(59, 130, 246, 0.1)'} 0%,
          transparent 70%);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .techy-cursor:hover + .cursor-particles {
        opacity: 1;
      }

      /* Cloud engineer themed effects */
      .techy-cursor::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6px;
        height: 6px;
        background: ${theme === 'dark' ? '#ffffff' : '#000000'};
        border-radius: 50%;
        transform: translate(-50%, -50%);
        opacity: 0.8;
      }

      .techy-cursor::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 1px solid ${theme === 'dark' ? 'rgba(88, 166, 255, 0.3)' : 'rgba(59, 130, 246, 0.3)'};
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        100% { transform: scale(1.5); opacity: 0; }
      }
    `;
    document.head.appendChild(style);

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mousemove', checkInteractiveElements);
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);

      if (document.body.contains(cursor)) document.body.removeChild(cursor);
      if (document.body.contains(trail)) document.body.removeChild(trail);
      if (document.body.contains(particles)) document.body.removeChild(particles);
      if (document.head.contains(style)) document.head.removeChild(style);
    };
  }, [theme]);

  return null;
}
