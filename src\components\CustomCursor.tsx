
import { useEffect, useState } from "react";
import { useTheme } from "@/hooks/use-theme";

export default function CustomCursor() {
  const { theme } = useTheme();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if device is mobile/tablet - don't show custom cursor on touch devices
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024 || 'ontouchstart' in window);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    // Don't initialize cursor on mobile devices
    if (isMobile) return;

    const cursorDot = document.createElement('div');
    const cursorOutline = document.createElement('div');

    cursorDot.classList.add('cursor-dot');
    cursorOutline.classList.add('cursor-outline');

    // Add theme-specific styling
    if (theme === 'dark') {
      cursorDot.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
      cursorOutline.style.borderColor = 'rgba(255, 255, 255, 0.5)';
    } else {
      cursorDot.style.backgroundColor = 'rgba(0, 0, 0, 0.9)';
      cursorOutline.style.borderColor = 'rgba(0, 0, 0, 0.5)';
    }

    document.body.appendChild(cursorDot);
    document.body.appendChild(cursorOutline);

    // Use requestAnimationFrame for smoother performance
    let cursorX = 0;
    let cursorY = 0;
    let outlineX = 0;
    let outlineY = 0;

    const render = () => {
      cursorDot.style.transform = `translate(${cursorX}px, ${cursorY}px)`;
      cursorOutline.style.transform = `translate(${outlineX}px, ${outlineY}px)`;
      requestAnimationFrame(render);
    };

    requestAnimationFrame(render);

    const handleMouseMove = (e: MouseEvent) => {
      cursorX = e.clientX;
      cursorY = e.clientY;

      // Smooth follow for outline with easing
      outlineX += (cursorX - outlineX) * 0.15;
      outlineY += (cursorY - outlineY) * 0.15;
    };

    const handleMouseEnter = () => {
      cursorDot.classList.add('cursor-hover');
      cursorOutline.classList.add('cursor-hover');
    };

    const handleMouseLeave = () => {
      cursorDot.classList.remove('cursor-hover');
      cursorOutline.classList.remove('cursor-hover');
    };

    document.addEventListener('mousemove', handleMouseMove);

    // Use event delegation for better performance
    document.addEventListener('mouseover', (e) => {
      if (e.target instanceof Element) {
        if (e.target.matches('a, button, input, textarea, select, [role="button"]')) {
          handleMouseEnter();
        }
      }
    });

    document.addEventListener('mouseout', (e) => {
      if (e.target instanceof Element) {
        if (e.target.matches('a, button, input, textarea, select, [role="button"]')) {
          handleMouseLeave();
        }
      }
    });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseover', handleMouseEnter);
      document.removeEventListener('mouseout', handleMouseLeave);

      document.body.removeChild(cursorDot);
      document.body.removeChild(cursorOutline);
    };
  }, [isMobile, theme]);

  return null;
}
