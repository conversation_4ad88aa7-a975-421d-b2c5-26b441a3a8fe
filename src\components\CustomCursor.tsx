
import { useEffect, useState, useRef } from "react";
import { useTheme } from "@/hooks/use-theme";

// High-performance techy sharp triangle cursor with cloud engineer vibes
export default function CustomCursor() {
  const { theme } = useTheme();
  const [visible, setVisible] = useState(false);
  const [isPointer, setIsPointer] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  const cursorRef = useRef<HTMLDivElement>(null);
  const trailRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Don't show custom cursor on mobile devices
    if ('ontouchstart' in window || window.innerWidth < 1024) {
      return;
    }

    // Create cursor elements
    const cursor = document.createElement('div');
    const trail = document.createElement('div');
    const glow = document.createElement('div');

    cursor.className = 'techy-triangle-cursor';
    trail.className = 'cursor-trail';
    glow.className = 'cursor-glow';

    document.body.appendChild(cursor);
    document.body.appendChild(trail);
    document.body.appendChild(glow);

    // High-performance cursor tracking with immediate updates
    let mouseX = 0;
    let mouseY = 0;
    let isAnimating = false;

    const updateCursor = () => {
      // Immediate cursor movement for responsiveness
      cursor.style.transform = `translate(${mouseX}px, ${mouseY}px)`;

      // Smooth trail with minimal delay
      const trailX = mouseX - 8;
      const trailY = mouseY - 8;
      trail.style.transform = `translate(${trailX}px, ${trailY}px)`;

      // Glow effect
      glow.style.transform = `translate(${mouseX}px, ${mouseY}px)`;

      isAnimating = false;
    };

    // Track mouse position with immediate updates
    const handleMouseMove = (e: MouseEvent) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
      setMousePos({ x: mouseX, y: mouseY });
      setVisible(true);

      // Use RAF only when not already animating for better performance
      if (!isAnimating) {
        isAnimating = true;
        requestAnimationFrame(updateCursor);
      }
    };

    // Check for interactive elements
    const checkInteractiveElements = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isClickable =
        target.tagName === 'A' ||
        target.tagName === 'BUTTON' ||
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.closest('a') ||
        target.closest('button') ||
        target.getAttribute('role') === 'button' ||
        target.classList.contains('cursor-pointer');

      const isTextInput = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA';

      setIsPointer(isClickable);
      setIsTyping(isTextInput);

      cursor.classList.toggle('cursor-hover', isClickable);
      cursor.classList.toggle('cursor-typing', isTextInput);
    };

    // Mouse events
    const handleMouseEnter = () => setVisible(true);
    const handleMouseLeave = () => setVisible(false);

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mousemove', checkInteractiveElements);
    document.addEventListener('mouseenter', handleMouseEnter);
    document.addEventListener('mouseleave', handleMouseLeave);

    // Add techy triangle cursor styles
    const style = document.createElement('style');
    style.innerHTML = `
      body, * {
        cursor: none !important;
      }

      .techy-triangle-cursor {
        position: fixed;
        top: 0;
        left: 0;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 12px solid ${theme === 'dark' ? '#58a6ff' : '#3b82f6'};
        pointer-events: none;
        z-index: 9999;
        transform: translate(-8px, -12px) rotate(-45deg);
        will-change: transform;
        filter: drop-shadow(0 0 8px ${theme === 'dark' ? 'rgba(88, 166, 255, 0.6)' : 'rgba(59, 130, 246, 0.6)'});
        transition: all 0.1s ease-out;
      }

      .cursor-trail {
        position: fixed;
        top: 0;
        left: 0;
        width: 16px;
        height: 16px;
        border: 1px solid ${theme === 'dark' ? 'rgba(88, 166, 255, 0.4)' : 'rgba(59, 130, 246, 0.4)'};
        border-radius: 50%;
        pointer-events: none;
        z-index: 9998;
        transform: translate(-50%, -50%);
        will-change: transform;
        opacity: 0.6;
      }

      .cursor-glow {
        position: fixed;
        top: 0;
        left: 0;
        width: 32px;
        height: 32px;
        background: radial-gradient(circle, ${theme === 'dark' ? 'rgba(88, 166, 255, 0.1)' : 'rgba(59, 130, 246, 0.1)'} 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
        z-index: 9997;
        transform: translate(-50%, -50%);
        will-change: transform;
        opacity: 0.8;
      }

      .techy-triangle-cursor.cursor-hover {
        border-bottom: 16px solid ${theme === 'dark' ? '#58a6ff' : '#3b82f6'};
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        filter: drop-shadow(0 0 12px ${theme === 'dark' ? 'rgba(88, 166, 255, 0.8)' : 'rgba(59, 130, 246, 0.8)'});
        transform: translate(-10px, -16px) rotate(-45deg) scale(1.2);
      }

      .techy-triangle-cursor.cursor-typing {
        width: 2px;
        height: 16px;
        border: none;
        border-radius: 1px;
        background: ${theme === 'dark' ? '#58a6ff' : '#3b82f6'};
        transform: translate(-1px, -8px);
        animation: blink 1s infinite;
      }

      @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0; }
      }

      .cursor-particles {
        position: fixed;
        top: 0;
        left: 0;
        width: 100px;
        height: 100px;
        pointer-events: none;
        z-index: 9997;
        transform: translate(-50%, -50%);
        background: radial-gradient(circle,
          ${theme === 'dark' ? 'rgba(88, 166, 255, 0.1)' : 'rgba(59, 130, 246, 0.1)'} 0%,
          transparent 70%);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .techy-cursor:hover + .cursor-particles {
        opacity: 1;
      }

      /* Cloud engineer themed effects */
      .techy-cursor::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 6px;
        height: 6px;
        background: ${theme === 'dark' ? '#ffffff' : '#000000'};
        border-radius: 50%;
        transform: translate(-50%, -50%);
        opacity: 0.8;
      }

      .techy-cursor::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 1px solid ${theme === 'dark' ? 'rgba(88, 166, 255, 0.3)' : 'rgba(59, 130, 246, 0.3)'};
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        100% { transform: scale(1.5); opacity: 0; }
      }
    `;
    document.head.appendChild(style);

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mousemove', checkInteractiveElements);
      document.removeEventListener('mouseenter', handleMouseEnter);
      document.removeEventListener('mouseleave', handleMouseLeave);

      if (document.body.contains(cursor)) document.body.removeChild(cursor);
      if (document.body.contains(trail)) document.body.removeChild(trail);
      if (document.body.contains(glow)) document.body.removeChild(glow);
      if (document.head.contains(style)) document.head.removeChild(style);
    };
  }, [theme]);

  return null;
}
