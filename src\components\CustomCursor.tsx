
import { useEffect, useState } from "react";

// Lightweight optimized cursor
export default function CustomCursor() {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [visible, setVisible] = useState(false);
  const [isPointer, setIsPointer] = useState(false);

  useEffect(() => {
    // Don't show custom cursor on mobile devices
    if ('ontouchstart' in window || window.innerWidth < 1024) {
      return;
    }

    // Create cursor elements
    const dot = document.createElement('div');
    dot.className = 'cursor-dot';
    document.body.appendChild(dot);

    // Track mouse position with throttling
    let lastUpdate = 0;
    const updateCursor = (e: MouseEvent) => {
      const now = performance.now();
      // Only update every 10ms for better performance
      if (now - lastUpdate < 10) return;
      lastUpdate = now;

      setPosition({ x: e.clientX, y: e.clientY });
      setVisible(true);

      // Apply position directly for smoother movement
      dot.style.transform = `translate(${e.clientX}px, ${e.clientY}px)`;
    };

    // Check if cursor is over clickable elements
    const checkPointerElements = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      const isClickable =
        target.tagName === 'A' ||
        target.tagName === 'BUTTON' ||
        target.tagName === 'INPUT' ||
        target.closest('a') ||
        target.closest('button') ||
        target.getAttribute('role') === 'button';

      setIsPointer(isClickable);

      if (isClickable) {
        dot.classList.add('cursor-pointer');
      } else {
        dot.classList.remove('cursor-pointer');
      }
    };

    // Hide cursor when leaving window
    const hideCursor = () => {
      setVisible(false);
      dot.style.opacity = '0';
    };

    // Show cursor when entering window
    const showCursor = () => {
      setVisible(true);
      dot.style.opacity = '1';
    };

    // Add event listeners
    document.addEventListener('mousemove', updateCursor);
    document.addEventListener('mousemove', checkPointerElements);
    document.addEventListener('mouseenter', showCursor);
    document.addEventListener('mouseleave', hideCursor);

    // Add cursor styles
    const style = document.createElement('style');
    style.innerHTML = `
      body {
        cursor: none !important;
      }
      a, button, input, [role="button"] {
        cursor: none !important;
      }
      .cursor-dot {
        position: fixed;
        top: 0;
        left: 0;
        width: 8px;
        height: 8px;
        background-color: #58a6ff;
        border-radius: 50%;
        pointer-events: none;
        z-index: 9999;
        transform: translate(-50%, -50%);
        transition: width 0.2s, height 0.2s, background-color 0.2s;
        will-change: transform;
      }
      .cursor-pointer {
        width: 24px;
        height: 24px;
        background-color: transparent;
        border: 2px solid #58a6ff;
      }
    `;
    document.head.appendChild(style);

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', updateCursor);
      document.removeEventListener('mousemove', checkPointerElements);
      document.removeEventListener('mouseenter', showCursor);
      document.removeEventListener('mouseleave', hideCursor);
      document.body.removeChild(dot);
      document.head.removeChild(style);
    };
  }, []);

  return null;
}
