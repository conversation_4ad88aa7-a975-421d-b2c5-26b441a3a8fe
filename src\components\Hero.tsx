
import { motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Linkedin, Mail, Terminal, ArrowRight, Cloud } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ContactModal } from "@/components/ContactModal";
import { useState } from "react";

export function Hero() {
  const [showContactModal, setShowContactModal] = useState(false);
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  const iconVariants = {
    hover: {
      scale: 1.2,
      rotate: 5,
      transition: { type: "spring", stiffness: 400, damping: 10 }
    }
  };

  const TextGradient = ({ children }: { children: React.ReactNode }) => (
    <span className="text-gradient">{children}</span>
  );

  return (
    <section id="home" className="min-h-screen pt-20 flex items-center relative overflow-hidden">
      <div className="container">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Profile Picture */}
          <motion.div
            variants={itemVariants}
            className="mb-8"
          >
            <motion.div
              className="relative w-32 h-32 mx-auto mb-6"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary via-accent to-primary animate-spin-slow"></div>
              <div className="absolute inset-1 rounded-full bg-background flex items-center justify-center">
                <div className="w-24 h-24 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center overflow-hidden">
                  {/* Replace with actual profile image */}
                  <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                    <Cloud className="h-10 w-10 text-white" />
                  </div>
                </div>
              </div>
              {/* Floating particles around profile */}
              <motion.div
                className="absolute -top-2 -right-2 w-4 h-4 bg-primary/60 rounded-full"
                animate={{
                  y: [0, -10, 0],
                  opacity: [0.6, 1, 0.6]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 3,
                  delay: 0
                }}
              />
              <motion.div
                className="absolute -bottom-2 -left-2 w-3 h-3 bg-accent/60 rounded-full"
                animate={{
                  y: [0, -8, 0],
                  opacity: [0.6, 1, 0.6]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 2.5,
                  delay: 1
                }}
              />
              <motion.div
                className="absolute top-1/2 -left-4 w-2 h-2 bg-primary/40 rounded-full"
                animate={{
                  x: [0, -5, 0],
                  opacity: [0.4, 0.8, 0.4]
                }}
                transition={{
                  repeat: Infinity,
                  duration: 2,
                  delay: 0.5
                }}
              />
            </motion.div>
          </motion.div>

          <motion.div variants={itemVariants} className="mb-6">
            <motion.p
              className="text-primary font-medium mb-2"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1 }}
            >
              Hi there, I'm
            </motion.p>
            <motion.h1
              className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
            >
              <TextGradient>Aditya Shenvi</TextGradient>
            </motion.h1>
            <motion.div
              className="overflow-hidden h-12 md:h-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <motion.p
                className="text-xl md:text-2xl text-foreground/80 font-medium"
                initial={{ y: 40 }}
                animate={{ y: 0 }}
                transition={{ duration: 0.8, delay: 0.7 }}
              >
                Turning coffee <span className="inline-block animate-pulse">☕</span> into CI/CD pipelines <span className="inline-block animate-float">🚀</span>
              </motion.p>
            </motion.div>
            <motion.p
              className="text-lg mt-2 text-muted-foreground"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 1, delay: 0.9 }}
            >
              3rd Year Engineering | Building tools that matter
            </motion.p>
          </motion.div>

          <motion.div variants={itemVariants} className="mb-8 max-w-2xl mx-auto">
            <p className="text-muted-foreground">
              I'm a developer and cloud enthusiast specializing in creating exceptional digital experiences.
              Currently focused on building accessible, human-centered products with modern technologies.
            </p>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex flex-wrap gap-4 justify-center mb-12"
          >
            <Link to="/#projects">
              <Button className="bg-primary hover:bg-primary/90 group">
                View Projects
                <motion.span
                  className="ml-2"
                  variants={{
                    rest: { x: 0 },
                    hover: { x: 4 }
                  }}
                  initial="rest"
                  whileHover="hover"
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <ArrowRight className="h-4 w-4" />
                </motion.span>
              </Button>
            </Link>
            <Button
              variant="outline"
              className="group"
              onClick={() => setShowContactModal(true)}
            >
              Contact Me
              <motion.span
                className="ml-2"
                variants={{
                  rest: { rotate: 0 },
                  hover: { rotate: 45 }
                }}
                initial="rest"
                whileHover="hover"
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Mail className="h-4 w-4" />
              </motion.span>
            </Button>
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="flex gap-4 justify-center"
          >
            <motion.a
              href="https://github.com"
              target="_blank"
              rel="noreferrer"
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
              whileHover="hover"
              variants={iconVariants}
            >
              <Github className="h-5 w-5" />
              <span className="sr-only">GitHub</span>
            </motion.a>
            <motion.a
              href="https://linkedin.com"
              target="_blank"
              rel="noreferrer"
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
              whileHover="hover"
              variants={iconVariants}
            >
              <Linkedin className="h-5 w-5" />
              <span className="sr-only">LinkedIn</span>
            </motion.a>
            <motion.a
              href="mailto:<EMAIL>"
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
              whileHover="hover"
              variants={iconVariants}
            >
              <Mail className="h-5 w-5" />
              <span className="sr-only">Email</span>
            </motion.a>
            <motion.a
              href="/terminal"
              className="bg-card hover:bg-card/80 p-2.5 rounded-full transition-colors"
              whileHover="hover"
              variants={iconVariants}
            >
              <Terminal className="h-5 w-5" />
              <span className="sr-only">Terminal Tools</span>
            </motion.a>
          </motion.div>
        </motion.div>
      </div>

      <motion.div
        className="absolute bottom-10 left-1/2 -translate-x-1/2"
        animate={{ y: [0, 10, 0] }}
        transition={{ repeat: Infinity, duration: 2 }}
      >
        <a href="#about" className="text-muted-foreground hover:text-foreground transition-colors">
          <ArrowDown className="h-6 w-6" />
          <span className="sr-only">Scroll down</span>
        </a>
      </motion.div>

      <ContactModal isOpen={showContactModal} onClose={() => setShowContactModal(false)} />
    </section>
  );
}
