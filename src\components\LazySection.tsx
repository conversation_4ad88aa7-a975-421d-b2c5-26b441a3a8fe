import React, { ReactNode, useEffect, useState, useRef } from 'react';
import { isAmpPage } from '@/utils/amp-utils';

interface LazySectionProps {
  children: ReactNode;
  threshold?: number;
  placeholder?: ReactNode;
  className?: string;
  id?: string;
}

/**
 * LazySection component that provides:
 * - Lazy loading of content when it comes into view
 * - AMP compatibility
 * - Placeholder while loading
 */
export const LazySection: React.FC<LazySectionProps> = ({
  children,
  threshold = 0.1,
  placeholder,
  className = '',
  id,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAmp, setIsAmp] = useState(false);
  const sectionRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    setIsAmp(isAmpPage());
    
    // If AMP, always show content
    if (isAmp) {
      setIsVisible(true);
      return;
    }
    
    // Use Intersection Observer for lazy loading
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        root: null,
        rootMargin: '100px',
        threshold,
      }
    );
    
    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }
    
    return () => {
      observer.disconnect();
    };
  }, [threshold, isAmp]);
  
  // Default placeholder
  const defaultPlaceholder = (
    <div className="animate-pulse w-full h-64 bg-card/20 rounded-lg"></div>
  );
  
  // If AMP, always render content
  if (isAmp) {
    return (
      <div id={id} className={className}>
        {children}
      </div>
    );
  }
  
  return (
    <div ref={sectionRef} id={id} className={className}>
      {isVisible ? children : (placeholder || defaultPlaceholder)}
    </div>
  );
};

export default LazySection;
