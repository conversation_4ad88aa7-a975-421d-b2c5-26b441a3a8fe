import { useTheme } from "@/hooks/use-theme";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  fullScreen?: boolean;
  text?: string;
}

export function LoadingSpinner({
  size = "md",
  className = "",
  fullScreen = false,
  text
}: LoadingSpinnerProps) {
  const { theme } = useTheme();
  
  const sizeClasses = {
    sm: "w-5 h-5 border-2",
    md: "w-8 h-8 border-3",
    lg: "w-12 h-12 border-4"
  };
  
  const spinnerClass = `${sizeClasses[size]} rounded-full animate-spin border-t-transparent border-primary`;
  
  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-background/80 backdrop-blur-sm z-50">
        <div className={spinnerClass} />
        {text && <p className="mt-4 text-foreground/80">{text}</p>}
      </div>
    );
  }
  
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className={spinnerClass} />
      {text && <p className="mt-2 text-sm text-foreground/80">{text}</p>}
    </div>
  );
}
