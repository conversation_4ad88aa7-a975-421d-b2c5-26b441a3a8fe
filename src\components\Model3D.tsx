import React, { useState, useEffect } from 'react';
import { isAmpPage } from '@/utils/amp-utils';

interface Model3DProps {
  className?: string;
}

/**
 * A CSS-based 3D model component that doesn't use Three.js
 * This is more compatible with all browsers and AMP
 */
export function Model3D({ className = '' }: Model3DProps) {
  const [isAmp, setIsAmp] = useState(false);
  const [rotation, setRotation] = useState({ x: 15, y: 45 });

  // Check if we're in AMP mode
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Animation loop for rotation
  useEffect(() => {
    if (isAmp) return;

    const interval = setInterval(() => {
      setRotation(prev => ({
        x: prev.x,
        y: (prev.y + 0.5) % 360
      }));
    }, 50);

    return () => clearInterval(interval);
  }, [isAmp]);

  // Handle mouse movement for interactive rotation
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isAmp) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Calculate rotation based on mouse position
    const rotX = 15 - (y - rect.height / 2) / 10;
    const rotY = (x - rect.width / 2) / 10 + 45;

    setRotation({ x: rotX, y: rotY });
  };

  // For AMP pages, return a simplified static model
  if (isAmp) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-full flex items-center justify-center">
          <div className="relative w-64 h-48 bg-card rounded-md shadow-lg transform rotate-12">
            <div className="absolute inset-1 bg-primary/20 rounded-sm flex items-center justify-center">
              <div className="text-primary font-mono text-xs">
                &lt;code /&gt;
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative ${className} w-full h-full flex items-center justify-center perspective-800`}
      onMouseMove={handleMouseMove}
    >
      {/* 3D Laptop Model */}
      <div
        className="relative w-64 h-48 transform-style-3d"
        style={{
          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,
          transition: 'transform 0.1s ease-out'
        }}
      >
        {/* Laptop Base */}
        <div className="absolute w-64 h-2 bg-gray-800 rounded-md transform translate-z-8 bottom-0 shadow-xl" />

        {/* Laptop Keyboard */}
        <div className="absolute w-64 h-32 bg-gray-700 rounded-md transform translate-z-10 bottom-2 shadow-lg">
          <div className="absolute inset-2 grid grid-cols-10 grid-rows-3 gap-1">
            {Array.from({ length: 30 }).map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-sm" />
            ))}
          </div>
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-6 bg-gray-800 rounded-md" />
        </div>

        {/* Laptop Screen */}
        <div className="absolute w-60 h-40 bg-gray-800 rounded-md transform origin-bottom rotateX(-15deg) translate-z-10 translate-y-[-40px] shadow-lg">
          {/* Screen Bezel */}
          <div className="absolute inset-1 bg-black rounded-sm">
            {/* Screen Content */}
            <div className="absolute inset-1 bg-primary/20 rounded-sm overflow-hidden">
              {/* Code Lines */}
              <div className="flex flex-col gap-1 p-2">
                {Array.from({ length: 8 }).map((_, i) => (
                  <div
                    key={i}
                    className="h-1 bg-white/70 rounded-full"
                    style={{
                      width: `${30 + Math.random() * 50}%`,
                      opacity: 0.5 + Math.random() * 0.5
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add CSS for 3D transforms */}
      <style jsx>{`
        .perspective-800 {
          perspective: 800px;
        }

        .transform-style-3d {
          transform-style: preserve-3d;
        }

        .translate-z-8 {
          transform: translateZ(8px);
        }

        .translate-z-10 {
          transform: translateZ(10px);
        }
      `}</style>
    </div>
  );
}
