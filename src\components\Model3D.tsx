import React, { useState, useEffect, useRef, useMemo } from 'react';
import { isAmpPage } from '@/utils/amp-utils';
import { useTheme } from '@/hooks/use-theme';

interface Model3DProps {
  className?: string;
}

/**
 * A CSS-based 3D model component that doesn't use Three.js
 * This is more compatible with all browsers and AMP
 */
export function Model3D({ className = '' }: Model3DProps) {
  const { theme } = useTheme();
  const [isAmp, setIsAmp] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [rotation, setRotation] = useState({ x: 15, y: 45 });
  const modelRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const lastTimeRef = useRef<number>(0);

  // Check if we're in AMP mode
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Use Intersection Observer to only animate when visible
  useEffect(() => {
    if (isAmp) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    if (modelRef.current) {
      observer.observe(modelRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, [isAmp]);

  // Animation loop for rotation using requestAnimationFrame for better performance
  useEffect(() => {
    if (isAmp || !isVisible) return;

    const animate = (time: number) => {
      if (!lastTimeRef.current) lastTimeRef.current = time;
      const deltaTime = time - lastTimeRef.current;

      // Only update every ~16ms (60fps) for better performance
      if (deltaTime > 16) {
        setRotation(prev => ({
          x: prev.x,
          y: (prev.y + 0.2) % 360
        }));
        lastTimeRef.current = time;
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isAmp, isVisible]);

  // Throttled mouse movement handler for better performance
  const handleMouseMove = useMemo(() => {
    let lastExecution = 0;

    return (e: React.MouseEvent<HTMLDivElement>) => {
      if (isAmp) return;

      const now = performance.now();
      // Throttle to 30ms
      if (now - lastExecution < 30) return;

      lastExecution = now;

      const rect = e.currentTarget.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      // Calculate rotation based on mouse position
      const rotX = 15 - (y - rect.height / 2) / 10;
      const rotY = (x - rect.width / 2) / 10 + 45;

      setRotation({ x: rotX, y: rotY });
    };
  }, [isAmp]);

  // For AMP pages, return a simplified static model
  if (isAmp) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full h-full flex items-center justify-center">
          <div className="relative w-64 h-48 bg-card rounded-md shadow-lg transform rotate-12">
            <div className="absolute inset-1 bg-primary/20 rounded-sm flex items-center justify-center">
              <div className="text-primary font-mono text-xs">
                &lt;code /&gt;
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Memoize code lines to prevent re-renders
  const codeLines = useMemo(() => {
    return Array.from({ length: 8 }).map((_, i) => ({
      width: `${30 + Math.floor(Math.random() * 50)}%`,
      opacity: 0.5 + Math.floor(Math.random() * 5) / 10
    }));
  }, []);

  return (
    <div
      ref={modelRef}
      className={`relative ${className} w-full h-full flex items-center justify-center perspective-800`}
      onMouseMove={handleMouseMove}
    >
      {/* 3D Laptop Model */}
      <div
        className="relative w-64 h-48 transform-style-3d will-change-transform"
        style={{
          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,
          transition: 'transform 0.1s ease-out'
        }}
      >
        {/* Laptop Base */}
        <div className="absolute w-64 h-2 bg-gray-800 rounded-md transform translate-z-8 bottom-0 shadow-xl" />

        {/* Laptop Keyboard */}
        <div className="absolute w-64 h-32 bg-gray-700 rounded-md transform translate-z-10 bottom-2 shadow-lg">
          <div className="absolute inset-2 grid grid-cols-10 grid-rows-3 gap-1">
            {Array.from({ length: 30 }).map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-sm" />
            ))}
          </div>
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-6 bg-gray-800 rounded-md" />
        </div>

        {/* Laptop Screen */}
        <div className="absolute w-60 h-40 bg-gray-800 rounded-md transform origin-bottom rotateX(-15deg) translate-z-10 translate-y-[-40px] shadow-lg">
          {/* Screen Bezel */}
          <div className="absolute inset-1 bg-black rounded-sm">
            {/* Screen Content */}
            <div className="absolute inset-1 bg-primary/20 rounded-sm overflow-hidden">
              {/* Code Lines */}
              <div className="flex flex-col gap-1 p-2">
                {codeLines.map((line, i) => (
                  <div
                    key={i}
                    className="h-1 bg-white/70 rounded-full"
                    style={{
                      width: line.width,
                      opacity: line.opacity
                    }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add CSS for 3D transforms */}
      <style>{`
        .perspective-800 {
          perspective: 800px;
        }

        .transform-style-3d {
          transform-style: preserve-3d;
        }

        .translate-z-8 {
          transform: translateZ(8px);
        }

        .translate-z-10 {
          transform: translateZ(10px);
        }
      `}</style>
    </div>
  );
}
