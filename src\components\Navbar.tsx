
import React from "react";
import { useState, useEffect } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, X, ChevronDown, Terminal, Settings, Home, BarChart2, Code, Wrench, FileText, Shield } from "lucide-react";
import { ThemeToggle } from "@/components/ThemeToggle";
import { Button } from "@/components/ui/button";
import { ContactPopup } from "@/components/ContactPopup";
import { useNavbar } from "@/hooks/use-navbar";
import { motion, AnimatePresence } from "framer-motion";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { cn } from "@/lib/utils";

export function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const navbar = useNavbar();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    const checkAuth = () => {
      const loggedIn = localStorage.getItem("isLoggedIn") === "true";
      setIsAuthenticated(loggedIn);
    };

    window.addEventListener("scroll", handleScroll);
    checkAuth();

    // Re-check auth status when localStorage changes
    window.addEventListener("storage", checkAuth);

    return () => {
      window.removeEventListener("scroll", handleScroll);
      window.removeEventListener("storage", checkAuth);
    };
  }, []);

  // Close mobile menu when path changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  const ListItem = React.forwardRef<
    React.ElementRef<"a">,
    React.ComponentPropsWithoutRef<"a">
  >(({ className, title, children, ...props }, ref) => {
    return (
      <li>
        <NavigationMenuLink asChild>
          <a
            ref={ref}
            className={cn(
              "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
              className
            )}
            {...props}
          >
            <div className="text-sm font-medium leading-none">{title}</div>
            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
              {children}
            </p>
          </a>
        </NavigationMenuLink>
      </li>
    );
  });
  ListItem.displayName = "ListItem";

  const navLinks = [
    { name: "Home", href: "/", icon: <Home className="h-4 w-4 mr-2" /> },
    { name: "About", href: "/about", icon: <User className="h-4 w-4 mr-2" /> },
    { name: "Projects", href: "/projects", icon: <Code className="h-4 w-4 mr-2" /> },
    { name: "Terminal", href: "/terminal", icon: <Terminal className="h-4 w-4 mr-2" /> },
    { name: "Tools", href: "/tools", icon: <Wrench className="h-4 w-4 mr-2" /> },
  ];

  const ResumeComponent = navbar?.resumeComponent;

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "py-2 backdrop-blur-xl bg-background/75 border-b border-border/50 shadow-sm"
          : "py-4 bg-transparent"
      }`}
    >
      <div className="container flex items-center justify-between">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Link to="/" className="flex items-center gap-2">
            <span className="font-bold text-xl text-gradient">Aditya Shenvi</span>
          </Link>
        </motion.div>

        {/* Desktop Navigation */}
        <NavigationMenu className="hidden md:flex">
          <NavigationMenuList>
            <NavigationMenuItem>
              <Link to="/" className={cn(
                navigationMenuTriggerStyle(),
                location.pathname === "/" ? "bg-accent/50" : ""
              )}>
                <Home className="h-4 w-4 mr-2" />
                Home
              </Link>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuTrigger>
                <Code className="h-4 w-4 mr-2" />
                Developer
              </NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                  <ListItem
                    title="Terminal"
                    href="/terminal"
                  >
                    Live coding environment with 50+ languages
                  </ListItem>
                  <ListItem
                    title="Tools"
                    href="/tools"
                  >
                    Browser-based developer utilities
                  </ListItem>
                  <ListItem
                    title="Projects"
                    href="/projects"
                  >
                    My open-source and client projects
                  </ListItem>
                  <ListItem
                    title="Skills"
                    href="/#skills"
                  >
                    My technical skills and expertise
                  </ListItem>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>

            <NavigationMenuItem>
              <NavigationMenuTrigger>
                <FileText className="h-4 w-4 mr-2" />
                Content
              </NavigationMenuTrigger>
              <NavigationMenuContent>
                <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                  <ListItem
                    title="Blog"
                    href="/#blog"
                  >
                    Articles about cloud, DevOps and development
                  </ListItem>
                  <ListItem
                    title="About Me"
                    href="/about"
                  >
                    Learn more about my background
                  </ListItem>
                </ul>
              </NavigationMenuContent>
            </NavigationMenuItem>

            {isAuthenticated && (
              <NavigationMenuItem>
                <NavigationMenuTrigger>
                  <BarChart2 className="h-4 w-4 mr-2" />
                  Dashboard
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-2">
                    <ListItem
                      title="My Dashboard"
                      href="/dashboard"
                    >
                      View your personalized dashboard
                    </ListItem>
                    <ListItem
                      title="Admin Panel"
                      href="/admin-panel"
                    >
                      Site administration and management
                    </ListItem>
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>
            )}
          </NavigationMenuList>
        </NavigationMenu>

        <div className="hidden md:flex items-center gap-4">
          <ThemeToggle />

          {isAuthenticated ? (
            <Button size="sm" className="bg-primary hover:bg-primary/90" asChild>
              <Link to="/dashboard">Dashboard</Link>
            </Button>
          ) : (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  if (ResumeComponent) {
                    const resumeElement = document.getElementById("resume-modal");
                    if (resumeElement) {
                      resumeElement.classList.add("show");
                    }
                  }
                }}
              >
                Resume
              </Button>
              <ContactPopup />
              <Button size="sm" className="bg-primary hover:bg-primary/90" asChild>
                <Link to="/login">Login</Link>
              </Button>
            </>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="flex items-center gap-4 md:hidden">
          <ThemeToggle />
          <Button variant="ghost" size="icon" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 z-50 flex flex-col pt-20 pb-6 bg-background/95 backdrop-blur-md md:hidden"
          >
            <div className="container flex flex-col gap-4">
              {navLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.href}
                  className="flex items-center text-foreground py-3 border-b border-border/50 hover:text-primary transition-colors"
                >
                  {link.icon}
                  {link.name}
                </Link>
              ))}

              <div className="flex flex-col gap-3 mt-4">
                {isAuthenticated ? (
                  <>
                    <Button className="bg-primary hover:bg-primary/90" asChild>
                      <Link to="/dashboard">
                        <BarChart2 className="h-4 w-4 mr-2" />
                        Dashboard
                      </Link>
                    </Button>
                    <Button variant="outline" asChild>
                      <Link to="/admin-panel">
                        <Shield className="h-4 w-4 mr-2" />
                        Admin Panel
                      </Link>
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      variant="outline"
                      onClick={() => {
                        if (ResumeComponent) {
                          const resumeElement = document.getElementById("resume-modal");
                          if (resumeElement) {
                            resumeElement.classList.add("show");
                          }
                        }
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      Resume
                    </Button>
                    <ContactPopup
                      trigger={
                        <Button variant="outline" onClick={() => setIsMobileMenuOpen(false)}>
                          Contact Me
                        </Button>
                      }
                    />
                    <Button className="bg-primary hover:bg-primary/90" asChild>
                      <Link to="/login">Login</Link>
                    </Button>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}
