import React, { useState, useEffect } from 'react';
import { isAmpPage } from '@/utils/amp-utils';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  loading?: 'lazy' | 'eager';
  sizes?: string;
  placeholder?: string;
}

/**
 * OptimizedImage component that provides:
 * - Lazy loading
 * - Responsive images with srcset
 * - Blur-up loading effect
 * - AMP compatibility
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width = 800,
  height = 600,
  className = '',
  loading = 'lazy',
  sizes = '100vw',
  placeholder = 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA4IDYiPjxyZWN0IHdpZHRoPSI4IiBoZWlnaHQ9IjYiIGZpbGw9IiNlMmU4ZjAiLz48L3N2Zz4='
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isAmp, setIsAmp] = useState(false);
  
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Generate srcset for responsive images
  const generateSrcSet = () => {
    const widths = [320, 640, 960, 1280, 1920];
    return widths
      .map(w => {
        // For demonstration, we're just appending width parameter
        // In a real implementation, you might use an image CDN or resizing service
        const url = new URL(src, window.location.origin);
        url.searchParams.set('width', w.toString());
        return `${url.toString()} ${w}w`;
      })
      .join(', ');
  };

  // Handle image load
  const handleImageLoad = () => {
    setIsLoaded(true);
  };

  // If AMP, use amp-img
  if (isAmp) {
    return (
      <amp-img
        src={src}
        alt={alt}
        width={width}
        height={height}
        layout="responsive"
        class={className}
      />
    );
  }

  // Regular responsive image with lazy loading
  return (
    <div className="relative overflow-hidden" style={{ aspectRatio: `${width}/${height}` }}>
      {!isLoaded && (
        <div 
          className="absolute inset-0 bg-card/20 animate-pulse"
          style={{ 
            backgroundImage: `url(${placeholder})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
          }}
        />
      )}
      <img
        src={src}
        alt={alt}
        width={width}
        height={height}
        loading={loading}
        sizes={sizes}
        srcSet={generateSrcSet()}
        onLoad={handleImageLoad}
        className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-500`}
      />
    </div>
  );
};

export default OptimizedImage;
