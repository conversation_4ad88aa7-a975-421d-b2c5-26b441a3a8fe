import React, { useState, useEffect, ReactNode } from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { isAmpPage } from '@/utils/amp-utils';

interface OptimizedLinkProps extends Omit<LinkProps, 'to'> {
  to: string;
  children: ReactNode;
  prefetch?: boolean;
  external?: boolean;
  className?: string;
}

/**
 * OptimizedLink component that provides:
 * - Link prefetching for internal links
 * - AMP compatibility
 * - External link handling with proper attributes
 */
export const OptimizedLink: React.FC<OptimizedLinkProps> = ({
  to,
  children,
  prefetch = true,
  external = false,
  className = '',
  ...props
}) => {
  const [isPrefetched, setPrefetched] = useState(false);
  const [isAmp, setIsAmp] = useState(false);
  
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Handle prefetching
  useEffect(() => {
    if (prefetch && !isPrefetched && !external && !isAmp) {
      // Only prefetch internal links that haven't been prefetched yet
      const prefetchLink = () => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = to;
        link.as = 'document';
        document.head.appendChild(link);
        setPrefetched(true);
      };

      // Use requestIdleCallback for better performance
      if ('requestIdleCallback' in window) {
        // @ts-ignore - TypeScript doesn't have types for requestIdleCallback
        window.requestIdleCallback(prefetchLink, { timeout: 2000 });
      } else {
        // Fallback for browsers that don't support requestIdleCallback
        setTimeout(prefetchLink, 1000);
      }
    }
  }, [to, prefetch, isPrefetched, external, isAmp]);

  // For external links
  if (external) {
    return (
      <a
        href={to}
        target="_blank"
        rel="noopener noreferrer"
        className={className}
        {...props}
      >
        {children}
      </a>
    );
  }

  // For AMP pages
  if (isAmp) {
    return (
      <a
        href={to}
        className={className}
        {...props}
      >
        {children}
      </a>
    );
  }

  // For internal links
  return (
    <Link
      to={to}
      className={className}
      {...props}
    >
      {children}
    </Link>
  );
};

export default OptimizedLink;
