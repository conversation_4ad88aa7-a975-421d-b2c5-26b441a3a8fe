
import { useState, useEffect } from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { isAmpPage } from "@/utils/amp-utils";

export function RotatingCube({ className }: { className?: string }) {
  const isMobile = useIsMobile();
  const [isAmp, setIsAmp] = useState(false);
  const [rotation, setRotation] = useState({ x: 0, y: 0, z: 0 });
  const [isHovered, setIsHovered] = useState(false);

  // Check if we're in AMP mode
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Animation loop for rotation
  useEffect(() => {
    if (isAmp) return;

    const interval = setInterval(() => {
      setRotation(prev => ({
        x: prev.x + 0.2,
        y: prev.y + 0.3,
        z: prev.z + 0.1
      }));
    }, 50);

    return () => clearInterval(interval);
  }, [isAmp]);

  // Handle mouse interaction
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // For AMP pages, return a simplified static cube
  if (isAmp) {
    return (
      <div className={`w-full h-full flex items-center justify-center ${className}`}>
        <div className="w-32 h-32 bg-primary/20 border border-primary/50 rounded-md transform rotate-45"></div>
      </div>
    );
  }

  return (
    <div
      className={`w-full h-full flex items-center justify-center perspective-800 ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className="cube"
        style={{
          transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg) rotateZ(${rotation.z}deg)`,
          transition: isHovered ? 'transform 0.1s ease-out' : 'transform 0.5s ease-out',
          animationPlayState: isHovered ? 'paused' : 'running'
        }}
      >
        <div className="cube-face cube-face-front"></div>
        <div className="cube-face cube-face-back"></div>
        <div className="cube-face cube-face-right"></div>
        <div className="cube-face cube-face-left"></div>
        <div className="cube-face cube-face-top"></div>
        <div className="cube-face cube-face-bottom"></div>
      </div>

      <style>{`
        .perspective-800 {
          perspective: 800px;
        }

        .cube {
          position: relative;
          width: 100px;
          height: 100px;
          transform-style: preserve-3d;
        }

        .cube-face {
          position: absolute;
          width: 100px;
          height: 100px;
          border: 2px solid #58a6ff;
          background-color: rgba(88, 166, 255, 0.2);
          box-shadow: 0 0 10px rgba(88, 166, 255, 0.5);
        }

        .cube-face-front {
          transform: translateZ(50px);
        }

        .cube-face-back {
          transform: translateZ(-50px) rotateY(180deg);
        }

        .cube-face-right {
          transform: translateX(50px) rotateY(90deg);
        }

        .cube-face-left {
          transform: translateX(-50px) rotateY(-90deg);
        }

        .cube-face-top {
          transform: translateY(-50px) rotateX(90deg);
        }

        .cube-face-bottom {
          transform: translateY(50px) rotateX(-90deg);
        }
      `}</style>
    </div>
  );
}
