import React, { useEffect, useState, useRef } from 'react';
import { isAmpPage } from '@/utils/amp-utils';

interface SimpleParticlesProps {
  className?: string;
}

/**
 * A simplified CSS-based particles background that doesn't use Canvas or Three.js
 * This is more compatible with AMP and lighter for performance
 */
export function SimpleParticles({ className }: SimpleParticlesProps) {
  const [isAmp, setIsAmp] = useState(false);

  // Check if we're in AMP mode
  useEffect(() => {
    setIsAmp(isAmpPage());
  }, []);

  // Generate static particles for the background
  const generateParticles = () => {
    const particles = [];
    const count = 50;

    for (let i = 0; i < count; i++) {
      const size = Math.random() * 4 + 1;
      const left = Math.random() * 100;
      const top = Math.random() * 100;
      const duration = Math.random() * 20 + 10;
      const delay = Math.random() * 5;
      const opacity = Math.random() * 0.5 + 0.1;

      particles.push(
        <div
          key={i}
          className="absolute rounded-full"
          style={{
            width: `${size}px`,
            height: `${size}px`,
            left: `${left}%`,
            top: `${top}%`,
            backgroundColor: 'rgba(88, 166, 255, ' + opacity + ')',
            boxShadow: '0 0 10px rgba(88, 166, 255, 0.5)',
            animation: `float ${duration}s ease-in-out ${delay}s infinite alternate`,
          }}
        />
      );
    }

    return particles;
  };

  // For AMP pages, return a simplified static background
  if (isAmp) {
    return (
      <div
        className={`fixed inset-0 -z-10 opacity-50 ${className}`}
        style={{
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
          backgroundSize: '400% 400%',
        }}
      />
    );
  }

  return (
    <div className={`fixed inset-0 -z-10 overflow-hidden ${className}`}>
      <div
        className="absolute inset-0"
        style={{
          background: 'linear-gradient(135deg, #0f172a 0%, #1e293b 100%)',
          opacity: 0.8,
        }}
      />
      {generateParticles()}

      {/* Add animated gradient overlay */}
      <div
        className="absolute inset-0"
        style={{
          background: 'radial-gradient(circle at 50% 50%, rgba(88, 166, 255, 0.1) 0%, rgba(15, 23, 42, 0) 70%)',
          animation: 'pulse 8s ease-in-out infinite',
        }}
      />

      {/* Add CSS animations */}
      <style jsx>{`
        @keyframes float {
          0% { transform: translateY(0) translateX(0); }
          50% { transform: translateY(-10px) translateX(5px); }
          100% { transform: translateY(5px) translateX(-5px); }
        }

        @keyframes pulse {
          0%, 100% { opacity: 0.1; }
          50% { opacity: 0.2; }
        }
      `}</style>
    </div>
  );
}
