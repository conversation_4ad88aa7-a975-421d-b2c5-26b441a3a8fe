
import { motion } from "framer-motion";
import { useState, useEffect, useRef } from "react";
import {
  Server,
  Database,
  Cloud,
  Code,
  Layout,
  Terminal,
  GitBranch,
  BarChart,
  Cpu,
  Globe,
  Shield,
  PenTool
} from "lucide-react";

const skillCategories = [
  {
    title: "Frontend",
    icon: <Layout className="h-6 w-6" />,
    skills: ["React", "TypeScript", "TailwindCSS", "Next.js", "Framer Motion", "Three.js"]
  },
  {
    title: "Backend",
    icon: <Server className="h-6 w-6" />,
    skills: ["Node.js", "Express", "Python", "Java", "GraphQL", "REST API"]
  },
  {
    title: "Database",
    icon: <Database className="h-6 w-6" />,
    skills: ["MongoDB", "PostgreSQL", "Firebase", "Redis", "DynamoDB"]
  },
  {
    title: "DevOps",
    icon: <Terminal className="h-6 w-6" />,
    skills: ["<PERSON>er", "Kubernetes", "CI/CD", "<PERSON>", "GitHub Actions"]
  },
  {
    title: "AWS Cloud",
    icon: <Cloud className="h-6 w-6" />,
    skills: ["EC2", "S3", "Lambda", "CloudFormation", "EKS", "Fargate"]
  },
  {
    title: "Web Development",
    icon: <Globe className="h-6 w-6" />,
    skills: ["Responsive Design", "SEO", "Web Performance", "API Integration", "PWAs"]
  },
  {
    title: "Infrastructure",
    icon: <Cpu className="h-6 w-6" />,
    skills: ["Terraform", "CloudFormation", "Ansible", "Monitoring", "Auto Scaling"]
  },
  {
    title: "Security",
    icon: <Shield className="h-6 w-6" />,
    skills: ["IAM", "Security Groups", "WAF", "VPC", "Certificate Manager"]
  },
  {
    title: "Tools",
    icon: <GitBranch className="h-6 w-6" />,
    skills: ["Git", "VS Code", "Bash", "Postman", "Figma"]
  },
  {
    title: "Design",
    icon: <PenTool className="h-6 w-6" />,
    skills: ["UI/UX Basics", "Figma", "Web Design", "Responsive Layouts"]
  },
  {
    title: "Languages",
    icon: <Code className="h-6 w-6" />,
    skills: ["JavaScript", "TypeScript", "Python", "Java", "Go", "Bash"]
  },
  {
    title: "Analytics",
    icon: <BarChart className="h-6 w-6" />,
    skills: ["Google Analytics", "Grafana", "Kibana", "Prometheus"]
  }
];

export function Skills() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef<HTMLElement>(null);

  // Intersection Observer for performance optimization
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stop observing once visible
        }
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} id="skills" className="py-20 bg-secondary/30">
      <div className="container">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        >
          <h2 className="text-3xl font-bold mb-4">
            <span className="text-gradient">Tech Stack & Skills</span>
          </h2>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            A collection of technologies and tools I use to build robust and scalable applications.
            Hover over each card to see more details.
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0 }}
          animate={isVisible ? { opacity: 1 } : { opacity: 0 }}
          transition={{ staggerChildren: 0.1, delay: 0.2 }}
        >
          {skillCategories.map((category, index) => (
            <motion.div
              key={category.title}
              className="glass group relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              animate={isVisible ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
              whileHover={{
                scale: 1.03,
                transition: { duration: 0.2 }
              }}
            >
              <div className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="text-primary">{category.icon}</div>
                  <h3 className="text-xl font-semibold">{category.title}</h3>
                </div>

                <ul className="space-y-2">
                  {category.skills.map((skill) => (
                    <li key={skill} className="text-muted-foreground text-sm flex items-center gap-2">
                      <span className="h-1.5 w-1.5 bg-primary rounded-full"></span>
                      {skill}
                    </li>
                  ))}
                </ul>
              </div>

              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
}
