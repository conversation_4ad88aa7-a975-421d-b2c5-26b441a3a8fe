
import { <PERSON>, Sun } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/hooks/use-theme";
import { motion } from "framer-motion";

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      className="relative h-9 w-9 rounded-full hover:bg-accent/10"
      aria-label={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      <div className="relative h-full w-full">
        {/* Sun icon */}
        <motion.div
          initial={{ opacity: theme === 'dark' ? 0 : 1, rotate: theme === 'dark' ? -45 : 0 }}
          animate={{ opacity: theme === 'dark' ? 0 : 1, rotate: theme === 'dark' ? -45 : 0 }}
          transition={{ duration: 0.2 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <Sun className={`h-5 w-5 ${theme === 'dark' ? 'text-muted-foreground' : 'text-amber-500'}`} />
        </motion.div>

        {/* Moon icon */}
        <motion.div
          initial={{ opacity: theme === 'dark' ? 1 : 0, rotate: theme === 'dark' ? 0 : 45 }}
          animate={{ opacity: theme === 'dark' ? 1 : 0, rotate: theme === 'dark' ? 0 : 45 }}
          transition={{ duration: 0.2 }}
          className="absolute inset-0 flex items-center justify-center"
        >
          <Moon className={`h-5 w-5 ${theme === 'dark' ? 'text-primary' : 'text-muted-foreground'}`} />
        </motion.div>
      </div>
    </Button>
  );
}
