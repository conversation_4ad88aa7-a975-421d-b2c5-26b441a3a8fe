import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Loader2, 
  Plus, 
  Edit, 
  Trash, 
  Save, 
  Eye, 
  Calendar,
  User,
  FileText,
  Upload,
  RefreshCw
} from 'lucide-react';
import { useWordPressAuth, useWordPressPosts, useWordPressMedia } from '@/hooks/use-wordpress-auth';
import { WordPressPost } from '@/services/wordpress';

interface PostEditorProps {
  post?: WordPressPost;
  onSave: (post: Partial<WordPressPost>) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

function PostEditor({ post, onSave, onCancel, isLoading = false }: PostEditorProps) {
  const [title, setTitle] = useState(post?.title?.rendered || '');
  const [content, setContent] = useState(post?.content?.rendered || '');
  const [excerpt, setExcerpt] = useState(post?.excerpt?.rendered || '');
  const [status, setStatus] = useState(post?.status || 'draft');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      title: { rendered: title },
      content: { rendered: content },
      excerpt: { rendered: excerpt },
      status: status as 'publish' | 'draft' | 'private',
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="title">Title</Label>
        <Input
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Enter post title"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="excerpt">Excerpt</Label>
        <Textarea
          id="excerpt"
          value={excerpt}
          onChange={(e) => setExcerpt(e.target.value)}
          placeholder="Brief description of the post"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="content">Content</Label>
        <Textarea
          id="content"
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder="Write your post content here..."
          rows={10}
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="status">Status</Label>
        <select
          id="status"
          value={status}
          onChange={(e) => setStatus(e.target.value)}
          className="w-full p-2 border rounded-md"
        >
          <option value="draft">Draft</option>
          <option value="publish">Published</option>
          <option value="private">Private</option>
        </select>
      </div>

      <div className="flex gap-2 justify-end">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              {post ? 'Update' : 'Create'} Post
            </>
          )}
        </Button>
      </div>
    </form>
  );
}

function PostsList() {
  const { posts, isLoading, error, fetchPosts, deletePost } = useWordPressPosts();
  const [selectedPost, setSelectedPost] = useState<WordPressPost | null>(null);

  useEffect(() => {
    fetchPosts({ per_page: 10 });
  }, []);

  const handleDelete = async (id: number) => {
    if (confirm('Are you sure you want to delete this post?')) {
      try {
        await deletePost(id);
      } catch (error) {
        console.error('Failed to delete post:', error);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">Loading posts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Recent Posts</h3>
        <Button size="sm" onClick={() => fetchPosts({ per_page: 10 })}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <ScrollArea className="h-[400px]">
        <div className="space-y-3">
          {posts.map((post) => (
            <Card key={post.id} className="p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h4 className="font-medium">{post.title.rendered}</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {post.excerpt.rendered.replace(/<[^>]*>/g, '').substring(0, 100)}...
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={post.status === 'publish' ? 'default' : 'outline'}>
                      {post.status}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      <Calendar className="h-3 w-3 inline mr-1" />
                      {new Date(post.date).toLocaleDateString()}
                    </span>
                  </div>
                </div>
                <div className="flex gap-1">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setSelectedPost(post)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDelete(post.id)}
                  >
                    <Trash className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
}

export function WordPressBlogManager() {
  const { isAuthenticated } = useWordPressAuth();
  const { createPost, updatePost, isLoading } = useWordPressPosts();
  const [activeTab, setActiveTab] = useState('posts');
  const [editingPost, setEditingPost] = useState<WordPressPost | null>(null);
  const [showEditor, setShowEditor] = useState(false);

  const handleSavePost = async (postData: Partial<WordPressPost>) => {
    try {
      if (editingPost) {
        await updatePost(editingPost.id, postData);
      } else {
        await createPost(postData);
      }
      setShowEditor(false);
      setEditingPost(null);
    } catch (error) {
      console.error('Failed to save post:', error);
    }
  };

  const handleEditPost = (post: WordPressPost) => {
    setEditingPost(post);
    setShowEditor(true);
  };

  if (!isAuthenticated) {
    return (
      <Alert>
        <AlertDescription>
          Please authenticate with WordPress to manage blog posts.
        </AlertDescription>
      </Alert>
    );
  }

  if (showEditor) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">
            {editingPost ? 'Edit Post' : 'Create New Post'}
          </h3>
        </div>
        <PostEditor
          post={editingPost || undefined}
          onSave={handleSavePost}
          onCancel={() => {
            setShowEditor(false);
            setEditingPost(null);
          }}
          isLoading={isLoading}
        />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Blog Management</h3>
        <Button onClick={() => setShowEditor(true)}>
          <Plus className="h-4 w-4 mr-2" />
          New Post
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="posts">
            <FileText className="h-4 w-4 mr-2" />
            Posts
          </TabsTrigger>
          <TabsTrigger value="media">
            <Upload className="h-4 w-4 mr-2" />
            Media
          </TabsTrigger>
        </TabsList>

        <TabsContent value="posts">
          <PostsList />
        </TabsContent>

        <TabsContent value="media">
          <div className="text-center p-8 text-muted-foreground">
            <Upload className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Media management coming soon...</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
