import { useState, useEffect } from 'react';

/**
 * Hook to detect if the current device is mobile
 * @returns {boolean} True if the device is mobile
 */
export function useIsMobile(): boolean {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if window is available (client-side)
    if (typeof window !== 'undefined') {
      // Initial check
      const checkMobile = () => {
        setIsMobile(window.innerWidth < 768);
      };
      
      // Check on mount
      checkMobile();
      
      // Add resize listener
      window.addEventListener('resize', checkMobile);
      
      // Clean up
      return () => {
        window.removeEventListener('resize', checkMobile);
      };
    }
  }, []);

  return isMobile;
}
