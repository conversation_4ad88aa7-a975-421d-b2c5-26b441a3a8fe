import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { wordpressService, WordPressUser, WordPressAuthResponse } from '@/services/wordpress';

interface WordPressAuthContextType {
  user: WordPressUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  error: string | null;
  clearError: () => void;
}

const WordPressAuthContext = createContext<WordPressAuthContextType | undefined>(undefined);

interface WordPressAuthProviderProps {
  children: ReactNode;
}

export function WordPressAuthProvider({ children }: WordPressAuthProviderProps) {
  const [user, setUser] = useState<WordPressUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if user is already authenticated on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (wordpressService.isAuthenticated()) {
          const isValid = await wordpressService.validateToken();
          
          if (isValid) {
            const currentUser = await wordpressService.getCurrentUser();
            setUser(currentUser);
          } else {
            // Token is invalid, clear it
            wordpressService.logout();
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
        wordpressService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const authResponse: WordPressAuthResponse = await wordpressService.authenticate(username, password);
      
      // Fetch user details after successful authentication
      const currentUser = await wordpressService.getCurrentUser();
      setUser(currentUser);
      
      console.log('WordPress login successful:', authResponse);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      setError(errorMessage);
      console.error('WordPress login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    wordpressService.logout();
    setUser(null);
    setError(null);
  };

  const clearError = () => {
    setError(null);
  };

  const value: WordPressAuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    error,
    clearError,
  };

  return (
    <WordPressAuthContext.Provider value={value}>
      {children}
    </WordPressAuthContext.Provider>
  );
}

export function useWordPressAuth() {
  const context = useContext(WordPressAuthContext);
  
  if (context === undefined) {
    throw new Error('useWordPressAuth must be used within a WordPressAuthProvider');
  }
  
  return context;
}

// Hook for WordPress posts management
export function useWordPressPosts() {
  const [posts, setPosts] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPosts = async (params?: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const fetchedPosts = await wordpressService.getPosts(params);
      setPosts(fetchedPosts);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch posts';
      setError(errorMessage);
      console.error('Error fetching posts:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createPost = async (postData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const newPost = await wordpressService.createPost(postData);
      setPosts(prevPosts => [newPost, ...prevPosts]);
      return newPost;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create post';
      setError(errorMessage);
      console.error('Error creating post:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updatePost = async (id: number, postData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      const updatedPost = await wordpressService.updatePost(id, postData);
      setPosts(prevPosts => 
        prevPosts.map(post => post.id === id ? updatedPost : post)
      );
      return updatedPost;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update post';
      setError(errorMessage);
      console.error('Error updating post:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deletePost = async (id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const success = await wordpressService.deletePost(id);
      if (success) {
        setPosts(prevPosts => prevPosts.filter(post => post.id !== id));
      }
      return success;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete post';
      setError(errorMessage);
      console.error('Error deleting post:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    posts,
    isLoading,
    error,
    fetchPosts,
    createPost,
    updatePost,
    deletePost,
    clearError: () => setError(null),
  };
}

// Hook for WordPress media management
export function useWordPressMedia() {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const uploadMedia = async (file: File) => {
    setIsUploading(true);
    setError(null);

    try {
      const media = await wordpressService.uploadMedia(file);
      return media;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload media';
      setError(errorMessage);
      console.error('Error uploading media:', error);
      throw error;
    } finally {
      setIsUploading(false);
    }
  };

  return {
    isUploading,
    error,
    uploadMedia,
    clearError: () => setError(null),
  };
}
