
/* Import AMP-specific styles */
@import './styles/amp.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Performance optimizations */
html {
  scroll-behavior: smooth;
}

/* Optimize scrolling performance */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Reduce layout shifts */
body {
  overflow-y: scroll;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Custom cursor styles removed for better performance */

/* Custom animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@layer base {
  :root {
    --background: 220 33% 98%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 220 91% 54%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 191 91% 54%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 220 91% 54%;
    --radius: 0.75rem;
  }

  .dark {
    --background: 222 47% 6%;
    --foreground: 210 40% 98%;

    --card: 225 32% 11%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 6%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 90% 60%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 32% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 32% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 191 91% 54%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 32% 17%;
    --input: 217 32% 17%;
    --ring: 224 76% 57%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    cursor: none; /* Hide default cursor for custom cursor */
  }

  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl;
  }

  .dark .glass {
    @apply bg-neo-card/40 backdrop-blur-md border border-white/10;
  }

  .glow {
    box-shadow: 0 0 10px theme('colors.neo.glow'), 0 0 20px theme('colors.neo.glow');
  }

  .text-glow {
    text-shadow: 0 0 10px theme('colors.primary', 'blue'), 0 0 20px theme('colors.primary', 'blue');
  }

  .neo-border {
    @apply border border-white/10;
  }

  .neo-card {
    @apply bg-neo-card/60 backdrop-blur-lg neo-border rounded-xl;
  }

  /* Custom cursor styles */
  .cursor-dot {
    position: fixed;
    top: 0;
    left: 0;
    width: 8px;
    height: 8px;
    background-color: hsl(var(--primary));
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 9999;
    transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
  }

  .cursor-outline {
    position: fixed;
    top: 0;
    left: 0;
    width: 40px;
    height: 40px;
    border: 2px solid hsla(var(--primary), 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 9998;
    transition: width 0.15s ease-in-out, height 0.15s ease-in-out, opacity 0.15s ease-in-out;
  }

  /* Hide custom cursor on touch devices */
  @media (hover: none) and (pointer: coarse) {
    .cursor-dot, .cursor-outline {
      display: none;
    }
    body {
      cursor: auto;
    }
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent;
  }

  .text-glow {
    text-shadow: 0 0 10px theme('colors.neo.glow'), 0 0 20px theme('colors.neo.glow');
  }

  /* Marquee animation for latest updates */
  @keyframes marquee {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  /* Enhanced animation keyframes */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { opacity: 1; box-shadow: 0 0 5px rgba(88, 166, 255, 0.7); }
    50% { opacity: 0.7; box-shadow: 0 0 15px rgba(88, 166, 255, 0.9); }
  }

  @keyframes rotate-slow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-rotate-slow {
    animation: rotate-slow 10s linear infinite;
  }

  /* Link hover effect */
  .hover-underline {
    position: relative;
  }

  .hover-underline::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: hsl(var(--primary));
    transform: scaleX(0);
    transform-origin: bottom right;
    transition: transform 0.3s ease-out;
  }

  .hover-underline:hover::after {
    transform: scaleX(1);
    transform-origin: bottom left;
  }
}

/* Mobile optimizations */
@media (max-width: 768px) {
  :root {
    font-size: 14px;
  }
}
