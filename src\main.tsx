import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Register service worker for PWA support
if ('serviceWorker' in navigator && import.meta.env.PROD) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('Service Worker registered with scope:', registration.scope);
      })
      .catch(error => {
        console.error('Service Worker registration failed:', error);
      });
  });
}

// Initialize performance monitoring
const reportWebVitals = () => {
  if (import.meta.env.PROD) {
    // Only measure in production
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(console.log); // Cumulative Layout Shift
      getFID(console.log); // First Input Delay
      getFCP(console.log); // First Contentful Paint
      getLCP(console.log); // Largest Contentful Paint
      getTTFB(console.log); // Time to First Byte
    });
  }
};

// Render the app
createRoot(document.getElementById("root")!).render(<App />);

// Report web vitals
reportWebVitals();
