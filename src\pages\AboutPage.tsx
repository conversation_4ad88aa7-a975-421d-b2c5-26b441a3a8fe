import { motion } from "framer-motion";
import { Navbar } from "@/components/Navbar";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { SimpleParticles } from "@/components/SimpleParticles";
import { ContactModal } from "@/components/ContactModal";
import { useState } from "react";
import {
  MapPin,
  Calendar,
  GraduationCap,
  Briefcase,
  Award,
  Heart,
  Coffee,
  Code,
  Cloud,
  Server,
  Database,
  Mail,
  Github,
  Linkedin,
  Download
} from "lucide-react";

const AboutPage = () => {
  const [showContactModal, setShowContactModal] = useState(false);

  const experiences = [
    {
      title: "Cloud Engineer Intern",
      company: "Tech Solutions Inc.",
      period: "2024 - Present",
      description: "Working on AWS infrastructure, CI/CD pipelines, and containerization with Docker and Kubernetes.",
      skills: ["AWS", "Docker", "Kubernetes", "CI/CD", "Terraform"]
    },
    {
      title: "Full Stack Developer",
      company: "Freelance",
      period: "2023 - 2024",
      description: "Developed web applications using React, Node.js, and various cloud services for multiple clients.",
      skills: ["React", "Node.js", "MongoDB", "Firebase", "TypeScript"]
    },
    {
      title: "Software Development Intern",
      company: "StartupXYZ",
      period: "2023",
      description: "Built responsive web applications and contributed to the development of mobile applications.",
      skills: ["JavaScript", "React Native", "Python", "PostgreSQL"]
    }
  ];

  const education = [
    {
      degree: "Bachelor of Technology",
      field: "Computer Science Engineering",
      institution: "ICFAI University",
      period: "2022 - 2026",
      grade: "8.5 CGPA",
      description: "Specializing in Cloud Computing and DevOps. Active member of the coding club and tech societies."
    }
  ];

  const achievements = [
    "🏆 Winner - College Hackathon 2024",
    "🥇 First Place - Cloud Computing Competition",
    "📜 AWS Cloud Practitioner Certified",
    "🎯 Google Cloud Associate Certified",
    "💡 Published 3 technical articles on Medium",
    "🚀 Contributed to 10+ open source projects"
  ];

  const interests = [
    { icon: <Cloud className="h-5 w-5" />, name: "Cloud Architecture" },
    { icon: <Server className="h-5 w-5" />, name: "DevOps & CI/CD" },
    { icon: <Database className="h-5 w-5" />, name: "Database Design" },
    { icon: <Code className="h-5 w-5" />, name: "Open Source" },
    { icon: <Coffee className="h-5 w-5" />, name: "Coffee Brewing" },
    { icon: <Heart className="h-5 w-5" />, name: "Tech Blogging" }
  ];

  return (
    <div className="min-h-screen bg-background">
      <SimpleParticles />
      <Navbar />
      
      <div className="container py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-16">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="relative w-32 h-32 mx-auto mb-6"
            >
              <div className="w-full h-full rounded-full bg-gradient-to-r from-primary to-accent p-1">
                <div className="w-full h-full rounded-full bg-background flex items-center justify-center">
                  <div className="w-24 h-24 rounded-full bg-gradient-to-r from-primary/20 to-accent/20 flex items-center justify-center">
                    <Code className="h-12 w-12 text-primary" />
                  </div>
                </div>
              </div>
            </motion.div>
            
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              <span className="text-gradient">About Me</span>
            </h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              Passionate Cloud Engineer & Full Stack Developer turning ideas into scalable solutions
            </p>
          </div>

          {/* Personal Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mb-16"
          >
            <Card className="neo-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-primary" />
                  Who I Am
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground leading-relaxed">
                  I'm Aditya Shenvi, a passionate 3rd-year Computer Science Engineering student with a deep love for 
                  cloud technologies and modern web development. My journey in tech started with curiosity about how 
                  things work behind the scenes, and it has evolved into a mission to build scalable, efficient, and 
                  user-friendly solutions.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  When I'm not coding, you'll find me exploring the latest cloud services, contributing to open source 
                  projects, or writing technical articles to share knowledge with the community. I believe in the power 
                  of technology to solve real-world problems and make a positive impact.
                </p>
                
                <div className="flex flex-wrap gap-4 pt-4">
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span>India</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-primary" />
                    <span>Available for opportunities</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Coffee className="h-4 w-4 text-primary" />
                    <span>Coffee enthusiast</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Experience */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Briefcase className="h-6 w-6 text-primary" />
              Experience
            </h2>
            <div className="space-y-6">
              {experiences.map((exp, index) => (
                <Card key={index} className="neo-card">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-lg">{exp.title}</h3>
                        <p className="text-primary font-medium">{exp.company}</p>
                      </div>
                      <Badge variant="outline">{exp.period}</Badge>
                    </div>
                    <p className="text-muted-foreground mb-4">{exp.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {exp.skills.map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="secondary" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Education */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <GraduationCap className="h-6 w-6 text-primary" />
              Education
            </h2>
            <div className="space-y-6">
              {education.map((edu, index) => (
                <Card key={index} className="neo-card">
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-semibold text-lg">{edu.degree}</h3>
                        <p className="text-primary font-medium">{edu.field}</p>
                        <p className="text-muted-foreground">{edu.institution}</p>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline" className="mb-2">{edu.period}</Badge>
                        <p className="text-sm font-medium">{edu.grade}</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground">{edu.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* Achievements */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-2">
              <Award className="h-6 w-6 text-primary" />
              Achievements
            </h2>
            <Card className="neo-card">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {achievements.map((achievement, index) => (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-secondary/20">
                      <span className="text-lg">{achievement}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Interests */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-bold mb-6">Interests & Hobbies</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {interests.map((interest, index) => (
                <Card key={index} className="neo-card hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-4 text-center">
                    <div className="text-primary mb-2">{interest.icon}</div>
                    <p className="text-sm font-medium">{interest.name}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </motion.div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
            className="text-center"
          >
            <Card className="neo-card">
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-4">Let's Work Together</h2>
                <p className="text-muted-foreground mb-6">
                  I'm always open to discussing new opportunities, interesting projects, or just having a chat about technology.
                </p>
                <div className="flex flex-wrap justify-center gap-4">
                  <Button onClick={() => setShowContactModal(true)}>
                    <Mail className="mr-2 h-4 w-4" />
                    Get In Touch
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="/resume.pdf" target="_blank" rel="noopener noreferrer">
                      <Download className="mr-2 h-4 w-4" />
                      Download Resume
                    </a>
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="https://github.com/adityashenvi" target="_blank" rel="noopener noreferrer">
                      <Github className="mr-2 h-4 w-4" />
                      GitHub
                    </a>
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="https://linkedin.com/in/adityashenvi" target="_blank" rel="noopener noreferrer">
                      <Linkedin className="mr-2 h-4 w-4" />
                      LinkedIn
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      </div>

      <ContactModal isOpen={showContactModal} onClose={() => setShowContactModal(false)} />
    </div>
  );
};

export default AboutPage;
