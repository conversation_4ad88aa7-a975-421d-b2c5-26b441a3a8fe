
import { useState, useEffect } from "react";
import { Navigate } from "react-router-dom";
import {
  <PERSON><PERSON><PERSON>,
  Users,
  FileCode,
  Settings,
  MessageSquare,
  Bell,
  Plus,
  Trash,
  Edit,
  Save,
  X,
  Wordpress
} from "lucide-react";
import { WordPressLogin, WordPressStatus, WordPressConnectionTest } from "@/components/WordPressLogin";
import { WordPressBlogManager } from "@/components/WordPressBlogManager";
import { useWordPressAuth, useWordPressPosts } from "@/hooks/use-wordpress-auth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";

// Mock authentication (in a real app would use Firebase or similar)
const useAdminAuth = () => {
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    // This is just for demonstration - would connect to a real auth system
    const admin = localStorage.getItem("isAdmin") === "true";
    setIsAdmin(admin);
  }, []);

  return { isAdmin };
};

// Mock data for projects
const PROJECTS = [
  {
    id: "proj-1",
    name: "Cloud Cost Optimizer",
    description: "A tool to optimize AWS cost and resources",
    status: "live",
    views: 1245,
    collaborators: 3
  },
  {
    id: "proj-2",
    name: "Kubernetes Dashboard",
    description: "A simplified K8s dashboard for monitoring",
    status: "live",
    views: 987,
    collaborators: 2
  },
  {
    id: "proj-3",
    name: "DevOps Toolkit",
    description: "Collection of CI/CD tools and scripts",
    status: "draft",
    views: 0,
    collaborators: 0
  }
];

// Mock data for users
const USERS = [
  {
    id: "user-1",
    name: "Jane Cooper",
    email: "<EMAIL>",
    role: "collaborator",
    lastActive: "2 hours ago",
    status: "active"
  },
  {
    id: "user-2",
    name: "John Smith",
    email: "<EMAIL>",
    role: "guest",
    lastActive: "1 day ago",
    status: "active"
  },
  {
    id: "user-3",
    name: "Alice Johnson",
    email: "<EMAIL>",
    role: "elite",
    lastActive: "Just now",
    status: "active"
  },
  {
    id: "user-4",
    name: "Bob Williams",
    email: "<EMAIL>",
    role: "partner",
    lastActive: "3 days ago",
    status: "inactive"
  }
];

// Mock data for messages
const MESSAGES = [
  {
    id: "msg-1",
    name: "Michael Brown",
    email: "<EMAIL>",
    message: "I'm interested in collaborating on your Cloud Cost Optimizer project. I have experience with AWS and cost optimization.",
    date: "2 hours ago",
    read: false
  },
  {
    id: "msg-2",
    name: "Sarah Davis",
    email: "<EMAIL>",
    message: "Your portfolio is impressive! Would love to discuss a potential project involving Kubernetes and CI/CD pipelines.",
    date: "1 day ago",
    read: true
  },
  {
    id: "msg-3",
    name: "David Wilson",
    email: "<EMAIL>",
    message: "Hi Aditya, I found your blog post on optimizing Docker containers very helpful. I have a question about multi-stage builds.",
    date: "3 days ago",
    read: true
  }
];

// Simple login component
const AdminLogin = ({ onLogin }: { onLogin: () => void }) => {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simple mock authentication - in a real app would use proper auth
    setTimeout(() => {
      if (username === "admin" && password === "password") {
        localStorage.setItem("isAdmin", "true");
        toast({
          title: "Admin access granted",
          description: "Welcome to the admin panel",
        });
        onLogin();
      } else {
        toast({
          title: "Authentication failed",
          description: "Invalid username or password",
          variant: "destructive",
        });
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Admin Login</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Username</label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full p-2 border rounded-md"
                required
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Password</label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-2 border rounded-md"
                required
              />
            </div>
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Authenticating..." : "Login"}
            </Button>

            <div className="text-center text-sm text-muted-foreground mt-4">
              <p>Demo credentials:</p>
              <p>Username: admin | Password: password</p>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

const AdminPanel = () => {
  const { isAdmin } = useAdminAuth();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [loggedIn, setLoggedIn] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    setLoggedIn(isAdmin);
  }, [isAdmin]);

  if (!loggedIn) {
    return <AdminLogin onLogin={() => setLoggedIn(true)} />;
  }

  const handleLogout = () => {
    localStorage.removeItem("isAdmin");
    setLoggedIn(false);
    toast({
      title: "Logged out",
      description: "You have been logged out of the admin panel",
    });
  };

  const handleRoleChange = (userId: string, newRole: string) => {
    toast({
      title: "Role updated",
      description: `User role has been updated to ${newRole}`,
    });
  };

  const handleDeleteUser = (userId: string) => {
    toast({
      title: "User deleted",
      description: "User has been removed from the system",
    });
  };

  const handleMarkAsRead = (msgId: string) => {
    toast({
      title: "Message marked as read",
      description: "Message has been marked as read",
    });
  };

  const handleReply = (msgId: string) => {
    toast({
      title: "Reply sent",
      description: "Your reply has been sent successfully",
    });
  };

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-10">
        <div className="container flex h-16 items-center justify-between py-4">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold text-gradient">Admin Panel</h1>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm">
              <Bell className="h-4 w-4 mr-2" />
              <span>3</span>
            </Button>
            <Button variant="destructive" size="sm" onClick={handleLogout}>
              Logout
            </Button>
          </div>
        </div>
      </header>

      <div className="container py-8 grid grid-cols-1 lg:grid-cols-[240px_1fr] gap-8">
        <aside className="hidden lg:block">
          <div className="sticky top-24 space-y-4">
            <Button
              variant={activeTab === "dashboard" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("dashboard")}
            >
              <BarChart className="mr-2 h-4 w-4" />
              Dashboard
            </Button>
            <Button
              variant={activeTab === "projects" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("projects")}
            >
              <FileCode className="mr-2 h-4 w-4" />
              Projects
            </Button>
            <Button
              variant={activeTab === "users" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("users")}
            >
              <Users className="mr-2 h-4 w-4" />
              Users
            </Button>
            <Button
              variant={activeTab === "messages" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("messages")}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Messages
            </Button>
            <Button
              variant={activeTab === "wordpress" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("wordpress")}
            >
              <Wordpress className="mr-2 h-4 w-4" />
              WordPress
            </Button>
            <Button
              variant={activeTab === "settings" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("settings")}
            >
              <Settings className="mr-2 h-4 w-4" />
              Settings
            </Button>
          </div>
        </aside>

        <main className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="lg:hidden">
            <TabsList className="grid grid-cols-6">
              <TabsTrigger value="dashboard">
                <BarChart className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="projects">
                <FileCode className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="users">
                <Users className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="messages">
                <MessageSquare className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="wordpress">
                <Wordpress className="h-4 w-4" />
              </TabsTrigger>
              <TabsTrigger value="settings">
                <Settings className="h-4 w-4" />
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Dashboard Tab */}
          <TabsContent value="dashboard" className="space-y-6">
            <h2 className="text-2xl font-bold tracking-tight">Dashboard</h2>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">24</div>
                  <p className="text-xs text-muted-foreground">
                    +12% from last month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
                  <FileCode className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">12</div>
                  <p className="text-xs text-muted-foreground">
                    +2 new this month
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Messages</CardTitle>
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">32</div>
                  <p className="text-xs text-muted-foreground">
                    8 unread messages
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tool Usage</CardTitle>
                  <Settings className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">573</div>
                  <p className="text-xs text-muted-foreground">
                    +201 from last week
                  </p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px] w-full">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-card rounded-lg border">
                      <div>
                        <p className="font-medium">New user registered</p>
                        <p className="text-sm text-muted-foreground"><EMAIL></p>
                      </div>
                      <Badge>Just now</Badge>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-card rounded-lg border">
                      <div>
                        <p className="font-medium">Project updated</p>
                        <p className="text-sm text-muted-foreground">Cloud Cost Optimizer</p>
                      </div>
                      <Badge>2 hours ago</Badge>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-card rounded-lg border">
                      <div>
                        <p className="font-medium">New message received</p>
                        <p className="text-sm text-muted-foreground">From: <EMAIL></p>
                      </div>
                      <Badge>3 hours ago</Badge>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-card rounded-lg border">
                      <div>
                        <p className="font-medium">New collaborator added</p>
                        <p className="text-sm text-muted-foreground">To: DevOps Toolkit</p>
                      </div>
                      <Badge>Yesterday</Badge>
                    </div>

                    <div className="flex justify-between items-center p-3 bg-card rounded-lg border">
                      <div>
                        <p className="font-medium">Blog post published</p>
                        <p className="text-sm text-muted-foreground">Optimizing Docker Containers</p>
                      </div>
                      <Badge>3 days ago</Badge>
                    </div>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Projects Tab */}
          <TabsContent value="projects" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold tracking-tight">Projects</h2>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Project
              </Button>
            </div>

            <div className="space-y-4">
              {PROJECTS.map((project) => (
                <Card key={project.id}>
                  <CardContent className="p-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                      <div>
                        <h3 className="font-medium">{project.name}</h3>
                        <p className="text-sm text-muted-foreground">{project.description}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant={project.status === "live" ? "default" : "outline"}>
                            {project.status === "live" ? "Live" : "Draft"}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Views: {project.views}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            Collaborators: {project.collaborators}
                          </span>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button variant={project.status === "live" ? "outline" : "default"} size="sm">
                          {project.status === "live" ? "Unpublish" : "Publish"}
                        </Button>
                        <Button variant="destructive" size="sm">
                          <Trash className="h-4 w-4 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold tracking-tight">Users</h2>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Invite User
              </Button>
            </div>

            <div className="space-y-4">
              {USERS.map((user) => (
                <Card key={user.id}>
                  <CardContent className="p-4">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                      <div>
                        <h3 className="font-medium">{user.name}</h3>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="outline">
                            {user.role}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            Last active: {user.lastActive}
                          </span>
                          <Badge variant={user.status === "active" ? "default" : "secondary"}>
                            {user.status}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <select
                          className="p-1 text-sm border rounded-md"
                          defaultValue={user.role}
                          onChange={(e) => handleRoleChange(user.id, e.target.value)}
                        >
                          <option value="guest">Guest</option>
                          <option value="collaborator">Collaborator</option>
                          <option value="elite">Elite</option>
                          <option value="partner">Partner</option>
                        </select>
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-4 w-4 mr-1" />
                          Message
                        </Button>
                        <Button variant="destructive" size="sm" onClick={() => handleDeleteUser(user.id)}>
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Messages Tab */}
          <TabsContent value="messages" className="space-y-6">
            <h2 className="text-2xl font-bold tracking-tight">Messages</h2>

            <div className="space-y-4">
              {MESSAGES.map((message) => (
                <Card key={message.id} className={!message.read ? "border-primary" : ""}>
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      <div className="flex justify-between">
                        <div>
                          <h3 className="font-medium">{message.name}</h3>
                          <p className="text-sm text-muted-foreground">{message.email}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{message.date}</Badge>
                          {!message.read && <Badge>New</Badge>}
                        </div>
                      </div>

                      <p className="text-sm border-l-2 pl-4 py-2">{message.message}</p>

                      <div className="flex justify-end gap-2">
                        {!message.read && (
                          <Button variant="outline" size="sm" onClick={() => handleMarkAsRead(message.id)}>
                            Mark as Read
                          </Button>
                        )}
                        <Button size="sm" onClick={() => handleReply(message.id)}>
                          Reply
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* WordPress Tab */}
          <TabsContent value="wordpress" className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold tracking-tight">WordPress Integration</h2>
              <WordPressStatus />
            </div>

            <WordPressConnectionTest />

            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>WordPress Authentication</CardTitle>
                </CardHeader>
                <CardContent>
                  <WordPressLogin onSuccess={() => {
                    toast({
                      title: "WordPress Connected",
                      description: "Successfully connected to WordPress backend",
                    });
                  }} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Blog Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <WordPressBlogManager />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <h2 className="text-2xl font-bold tracking-tight">Settings</h2>

            <Card>
              <CardHeader>
                <CardTitle>Site Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Site Title</label>
                  <input
                    type="text"
                    defaultValue="Aditya Shenvi 🚀 | Developer | Cloud & DevOps Enthusiast"
                    className="w-full p-2 border rounded-md"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Site Description</label>
                  <textarea
                    defaultValue="Aditya Shenvi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines."
                    className="w-full p-2 border rounded-md h-20"
                  />
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Enable Collaboration</label>
                  <div className="flex items-center">
                    <input type="checkbox" defaultChecked />
                    <span className="ml-2 text-sm">Allow users to request collaboration on projects</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Google Analytics ID</label>
                  <input
                    type="text"
                    placeholder="UA-XXXXXXXXX-X"
                    className="w-full p-2 border rounded-md"
                  />
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    Save Settings
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Email Notifications</label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">New user registrations</span>
                      <input type="checkbox" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">New messages</span>
                      <input type="checkbox" defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Collaboration requests</span>
                      <input type="checkbox" defaultChecked />
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Email Address for Notifications</label>
                  <input
                    type="email"
                    placeholder="<EMAIL>"
                    className="w-full p-2 border rounded-md"
                  />
                </div>

                <div className="flex justify-end">
                  <Button>
                    <Save className="mr-2 h-4 w-4" />
                    Save Notification Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </main>
      </div>
    </div>
  );
};

export default AdminPanel;
