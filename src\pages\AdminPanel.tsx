import { useState, useEffect } from "react";
import { Navbar } from "@/components/Navbar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/hooks/use-auth";
import { 
  getBlogPosts, 
  createBlogPost, 
  updateBlogPost, 
  deleteBlogPost,
  uploadFile 
} from "@/services/firebase";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  Upload, 
  Eye,
  Calendar,
  User,
  FileText
} from "lucide-react";
import { useNavigate } from "react-router-dom";

interface BlogPost {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  author: string;
  createdAt: any;
  updatedAt: any;
  published: boolean;
  tags: string[];
  imageUrl?: string;
}

const AdminPanel = () => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [loading, setLoading] = useState(false);
  const [imageFile, setImageFile] = useState<File | null>(null);
  
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();

  // Form state
  const [formData, setFormData] = useState({
    title: "",
    content: "",
    excerpt: "",
    tags: "",
    published: false
  });

  useEffect(() => {
    if (!user) {
      navigate("/login");
      return;
    }
    loadPosts();
  }, [user, navigate]);

  const loadPosts = async () => {
    try {
      setLoading(true);
      const blogPosts = await getBlogPosts();
      setPosts(blogPosts as BlogPost[]);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load blog posts",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.content) {
      toast({
        title: "Error",
        description: "Title and content are required",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoading(true);
      
      let imageUrl = editingPost?.imageUrl || "";
      
      // Upload image if selected
      if (imageFile) {
        const imagePath = `blog/${Date.now()}_${imageFile.name}`;
        imageUrl = await uploadFile(imageFile, imagePath);
      }

      const postData = {
        title: formData.title,
        content: formData.content,
        excerpt: formData.excerpt || formData.content.substring(0, 150) + "...",
        author: user?.displayName || user?.email || "Admin",
        tags: formData.tags.split(",").map(tag => tag.trim()).filter(Boolean),
        published: formData.published,
        imageUrl
      };

      if (editingPost) {
        await updateBlogPost(editingPost.id, postData);
        toast({
          title: "Success",
          description: "Blog post updated successfully"
        });
      } else {
        await createBlogPost(postData);
        toast({
          title: "Success",
          description: "Blog post created successfully"
        });
      }

      // Reset form
      setFormData({
        title: "",
        content: "",
        excerpt: "",
        tags: "",
        published: false
      });
      setEditingPost(null);
      setIsCreating(false);
      setImageFile(null);
      
      // Reload posts
      loadPosts();
      
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save blog post",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (post: BlogPost) => {
    setEditingPost(post);
    setFormData({
      title: post.title,
      content: post.content,
      excerpt: post.excerpt,
      tags: post.tags.join(", "),
      published: post.published
    });
    setIsCreating(true);
  };

  const handleDelete = async (postId: string) => {
    if (!confirm("Are you sure you want to delete this post?")) return;
    
    try {
      setLoading(true);
      await deleteBlogPost(postId);
      toast({
        title: "Success",
        description: "Blog post deleted successfully"
      });
      loadPosts();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete blog post",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <div className="container py-20">
        <div className="max-w-6xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gradient mb-2">Admin Panel</h1>
            <p className="text-muted-foreground">Manage your blog posts and content</p>
          </div>

          <Tabs defaultValue="posts" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="posts">Blog Posts</TabsTrigger>
              <TabsTrigger value="create">Create/Edit Post</TabsTrigger>
            </TabsList>

            <TabsContent value="posts" className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold">All Posts ({posts.length})</h2>
                <Button 
                  onClick={() => {
                    setIsCreating(true);
                    setEditingPost(null);
                    setFormData({
                      title: "",
                      content: "",
                      excerpt: "",
                      tags: "",
                      published: false
                    });
                  }}
                >
                  <Plus className="mr-2 h-4 w-4" />
                  New Post
                </Button>
              </div>

              {loading ? (
                <div className="text-center py-8">Loading posts...</div>
              ) : posts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  No blog posts found. Create your first post!
                </div>
              ) : (
                <div className="grid gap-4">
                  {posts.map((post) => (
                    <Card key={post.id} className="neo-card">
                      <CardHeader>
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <CardTitle className="text-lg">{post.title}</CardTitle>
                            <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <User className="h-3 w-3" />
                                {post.author}
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {post.createdAt?.toDate?.()?.toLocaleDateString() || "Unknown"}
                              </div>
                              <Badge variant={post.published ? "default" : "secondary"}>
                                {post.published ? "Published" : "Draft"}
                              </Badge>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEdit(post)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDelete(post.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground text-sm mb-2">{post.excerpt}</p>
                        {post.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {post.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="create" className="space-y-4">
              <Card className="neo-card">
                <CardHeader>
                  <CardTitle>
                    {editingPost ? "Edit Post" : "Create New Post"}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-1">Title</label>
                      <Input
                        value={formData.title}
                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                        placeholder="Enter post title"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Excerpt</label>
                      <Input
                        value={formData.excerpt}
                        onChange={(e) => setFormData({ ...formData, excerpt: e.target.value })}
                        placeholder="Brief description (optional)"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Content</label>
                      <Textarea
                        value={formData.content}
                        onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                        placeholder="Write your post content here..."
                        rows={10}
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Tags (comma separated)</label>
                      <Input
                        value={formData.tags}
                        onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
                        placeholder="react, javascript, tutorial"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-1">Featured Image</label>
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </div>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="published"
                        checked={formData.published}
                        onChange={(e) => setFormData({ ...formData, published: e.target.checked })}
                        className="rounded"
                      />
                      <label htmlFor="published" className="text-sm font-medium">
                        Publish immediately
                      </label>
                    </div>

                    <div className="flex gap-2">
                      <Button type="submit" disabled={loading}>
                        <Save className="mr-2 h-4 w-4" />
                        {loading ? "Saving..." : editingPost ? "Update Post" : "Create Post"}
                      </Button>
                      {isCreating && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setIsCreating(false);
                            setEditingPost(null);
                            setFormData({
                              title: "",
                              content: "",
                              excerpt: "",
                              tags: "",
                              published: false
                            });
                          }}
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;
