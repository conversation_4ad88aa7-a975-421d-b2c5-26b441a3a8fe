import { lazy, Suspense, useEffect, useState } from "react";
import { Navbar } from "@/components/Navbar";
import { <PERSON> } from "@/components/Hero";
import { AmpHero } from "@/components/AmpHero";
import { Resume } from "@/components/Resume";
import { SimpleParticles } from "@/components/SimpleParticles";
import { Link } from "react-router-dom";
import { ContactPopup } from "@/components/ContactPopup";
import { ArrowRight, Github, Mail, Twitter } from "lucide-react";
import { Button } from "@/components/ui/button";
import AmpWrapper from "@/components/AmpWrapper";
import { Helmet } from "react-helmet";
import { isAmpPage } from "@/utils/amp-utils";

// Lazy load non-critical components for better performance
const About = lazy(() => import("@/components/About").then(module => ({ default: module.About })));
const Skills = lazy(() => import("@/components/Skills").then(module => ({ default: module.Skills })));
const Projects = lazy(() => import("@/components/Projects").then(module => ({ default: module.Projects })));
const Blog = lazy(() => import("@/components/Blog").then(module => ({ default: module.Blog })));

// Register the Resume component in the Navbar
import { useNavbar } from "@/hooks/use-navbar";

// Loading fallback component
const SectionLoadingFallback = () => (
  <div className="py-16 flex items-center justify-center">
    <div className="animate-pulse w-full max-w-3xl h-64 bg-card/20 rounded-lg"></div>
  </div>
);

// Create a simple hook to register the Resume component
function useRegisterNavbarResume() {
  const navbar = useNavbar?.();

  useEffect(() => {
    if (navbar?.registerResumeComponent) {
      navbar.registerResumeComponent(Resume);
    }
  }, [navbar]);
}

const Index = () => {
  // Register the Resume component
  useRegisterNavbarResume();

  // State to track if we're in AMP mode
  const [isAmp, setIsAmp] = useState(false);

  useEffect(() => {
    // Check if we're in AMP mode
    setIsAmp(isAmpPage());

    // Scroll to section if URL has hash
    const hash = window.location.hash;
    if (hash) {
      const element = document.querySelector(hash);
      if (element) {
        setTimeout(() => {
          element.scrollIntoView({ behavior: 'smooth' });
        }, 500);
      }
    }
  }, []);

  return (
    <AmpWrapper>
      <div className="min-h-screen">
        <Helmet>
          <title>Aditya Shenvi 🚀 | Developer | Cloud & DevOps Enthusiast</title>
          <meta name="description" content="Aditya Shenvi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines." />
          <link rel="canonical" href="https://adityashenvi.com" />
          <meta property="og:title" content="Aditya Shenvi | Developer | Cloud & DevOps Enthusiast" />
          <meta property="og:description" content="Aditya Shenvi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines." />
          <meta property="og:url" content="https://adityashenvi.com" />
          <meta name="twitter:title" content="Aditya Shenvi | Developer | Cloud & DevOps Enthusiast" />
          <meta name="twitter:description" content="Aditya Shenvi - Developer & Cloud/DevOps Enthusiast. Turning coffee into CI/CD pipelines." />

          {/* AMP-specific meta tags */}
          {isAmp && (
            <>
              <meta name="amp-google-client-id-api" content="googleanalytics" />
              <script async custom-element="amp-analytics" src="https://cdn.ampproject.org/v0/amp-analytics-0.1.js"></script>
              <script async custom-element="amp-form" src="https://cdn.ampproject.org/v0/amp-form-0.1.js"></script>
              <script async custom-element="amp-bind" src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"></script>
            </>
          )}
        </Helmet>

        {/* Conditionally render particles based on AMP mode */}
        <SimpleParticles />

        <Navbar />

        <main>
          {/* Conditionally render the appropriate Hero component */}
          {isAmp ? <AmpHero /> : <Hero />}

          <Suspense fallback={<SectionLoadingFallback />}>
            <About />
          </Suspense>

          <Suspense fallback={<SectionLoadingFallback />}>
            <Skills />
          </Suspense>

          <Suspense fallback={<SectionLoadingFallback />}>
            <Projects />
          </Suspense>

          <Suspense fallback={<SectionLoadingFallback />}>
            <Blog />
          </Suspense>
        </main>

        <footer className="bg-secondary/10 py-12 border-t border-white/10">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div>
                <h3 className="text-xl font-bold text-gradient mb-4">Aditya Shenvi</h3>
                <p className="text-muted-foreground">
                  Cloud & DevOps engineer turning coffee into infrastructure code.
                </p>
                <div className="flex gap-4 mt-4">
                  <a
                    href="https://github.com"
                    target="_blank"
                    rel="noreferrer"
                    aria-label="GitHub"
                    className="bg-card hover:bg-card/80 p-2 rounded-full transition-colors"
                  >
                    <Github className="h-5 w-5" />
                  </a>
                  <a
                    href="https://twitter.com"
                    target="_blank"
                    rel="noreferrer"
                    aria-label="Twitter"
                    className="bg-card hover:bg-card/80 p-2 rounded-full transition-colors"
                  >
                    <Twitter className="h-5 w-5" />
                  </a>
                  <a
                    href="mailto:<EMAIL>"
                    aria-label="Email"
                    className="bg-card hover:bg-card/80 p-2 rounded-full transition-colors"
                  >
                    <Mail className="h-5 w-5" />
                  </a>
                </div>
              </div>

              <div>
                <h3 className="font-bold mb-4">Quick Links</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/#about" className="text-muted-foreground hover:text-primary transition-colors">
                      About Me
                    </Link>
                  </li>
                  <li>
                    <Link to="/#projects" className="text-muted-foreground hover:text-primary transition-colors">
                      Projects
                    </Link>
                  </li>
                  <li>
                    <Link to="/#blog" className="text-muted-foreground hover:text-primary transition-colors">
                      Blog
                    </Link>
                  </li>
                  <li>
                    <Link to="/tools" className="text-muted-foreground hover:text-primary transition-colors">
                      Developer Tools
                    </Link>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold mb-4">Resources</h3>
                <ul className="space-y-2">
                  <li>
                    <Link to="/terminal" className="text-muted-foreground hover:text-primary transition-colors">
                      Developer Terminal
                    </Link>
                  </li>
                  <li>
                    <Link to="/dashboard" className="text-muted-foreground hover:text-primary transition-colors">
                      Dashboard
                    </Link>
                  </li>
                  <li>
                    <Link to="/login" className="text-muted-foreground hover:text-primary transition-colors">
                      Login / Register
                    </Link>
                  </li>
                  <li>
                    <Link to="/admin" className="text-muted-foreground hover:text-primary transition-colors">
                      Admin Panel
                    </Link>
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="font-bold mb-4">Get in Touch</h3>
                <p className="text-muted-foreground mb-4">
                  Interested in working together? Reach out to discuss potential opportunities.
                </p>
                <ContactPopup
                  trigger={
                    <Button>
                      Contact Me <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  }
                />
              </div>
            </div>

            <div className="mt-12 pt-6 border-t border-white/10 flex flex-col md:flex-row justify-between items-center gap-4">
              <p className="text-sm text-muted-foreground">
                © {new Date().getFullYear()} Aditya Shenvi. All rights reserved.
              </p>
              <div className="flex gap-4">
                <Link to="/terms" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                  Terms & Conditions
                </Link>
                <Link to="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                  Privacy Policy
                </Link>
              </div>
            </div>
          </div>
        </footer>

        {/* Resume is rendered in the Navbar */}
      </div>
    </AmpWrapper>
  );
};

export default Index;
