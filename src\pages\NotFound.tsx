import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect, useState } from "react";
import { Helmet } from "react-helmet";
import { Navbar } from "@/components/Navbar";
import { Button } from "@/components/ui/button";
import { Home, Search, ArrowLeft, AlertTriangle } from "lucide-react";
import OptimizedLink from "@/components/OptimizedLink";
import { isAmpPage } from "@/utils/amp-utils";
import AmpWrapper from "@/components/AmpWrapper";

const NotFound = () => {
  const location = useLocation();
  const [isAmp, setIsAmp] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  useEffect(() => {
    // Check if we're in AMP mode
    setIsAmp(isAmpPage());

    // Log the 404 error
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );

    // Generate page suggestions based on the current path
    generateSuggestions(location.pathname);
  }, [location.pathname]);

  // Generate page suggestions based on the current path
  const generateSuggestions = (path: string) => {
    const availablePages = [
      { path: "/", name: "Home" },
      { path: "/dashboard", name: "Dashboard" },
      { path: "/login", name: "Login" },
      { path: "/terminal", name: "Terminal" },
      { path: "/tools", name: "Tools" },
      { path: "/terms", name: "Terms & Conditions" },
      { path: "/privacy", name: "Privacy Policy" },
    ];

    // Filter out pages that might be relevant
    const relevantPages = availablePages
      .filter(page => {
        // Skip the home page from suggestions if we're already suggesting other pages
        if (page.path === "/" && availablePages.length > 1) return false;

        // Check if the current path contains parts of the available path
        const pathParts = path.toLowerCase().split("/").filter(Boolean);
        const pageParts = page.path.toLowerCase().split("/").filter(Boolean);

        return pageParts.some(part =>
          pathParts.some(pathPart =>
            pathPart.includes(part) || part.includes(pathPart)
          )
        );
      })
      .map(page => page.path);

    // Always include home page if no suggestions
    if (relevantPages.length === 0) {
      relevantPages.push("/");
    }

    // Limit to 3 suggestions
    setSuggestions(relevantPages.slice(0, 3));
  };

  return (
    <AmpWrapper>
      <div className="min-h-screen bg-background">
        <Helmet>
          <title>404 - Page Not Found | Aditya Shenvi</title>
          <meta name="description" content="The page you're looking for doesn't exist or has been moved." />
          <meta name="robots" content="noindex, follow" />
        </Helmet>

        <Navbar />

        <main className="container py-20">
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-destructive/10 mb-6">
                <AlertTriangle className="h-10 w-10 text-destructive" />
              </div>
              <h1 className="text-5xl font-bold mb-4">
                <span className="text-gradient">404</span>
              </h1>
              <p className="text-2xl font-medium mb-2">Page Not Found</p>
              <p className="text-muted-foreground mb-8">
                The page you're looking for doesn't exist or has been moved.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
                <Button asChild size="lg" className="gap-2">
                  <Link to="/">
                    <Home className="h-4 w-4" />
                    Back to Home
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="gap-2">
                  <Link to={-1 as any}>
                    <ArrowLeft className="h-4 w-4" />
                    Go Back
                  </Link>
                </Button>
              </div>
            </div>

            {suggestions.length > 0 && (
              <div className="glass p-6 rounded-lg">
                <h2 className="text-xl font-medium mb-4">You might be looking for:</h2>
                <ul className="space-y-2">
                  {suggestions.map((suggestion) => (
                    <li key={suggestion}>
                      <OptimizedLink
                        to={suggestion}
                        className="text-primary hover:underline flex items-center gap-2"
                      >
                        <Search className="h-4 w-4" />
                        {suggestion === "/" ? "Home Page" : suggestion.substring(1)}
                      </OptimizedLink>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </main>
      </div>
    </AmpWrapper>
  );
};

export default NotFound;
