import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Navbar } from "@/components/Navbar";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import {
  ExternalLink,
  Github,
  Calendar,
  Star,
  GitBranch,
  Users,
  Code,
  Zap,
  Shield,
  Database,
  Search,
  Filter,
  Clock,
  Eye
} from "lucide-react";
import { SimpleParticles } from "@/components/SimpleParticles";
import { useToast } from "@/components/ui/use-toast";

const PROJECTS = [
  {
    id: "safebite",
    title: "SafeBite - Food Safety Platform",
    description: "A comprehensive food safety platform with AI-powered risk assessment, real-time monitoring, and compliance tracking for restaurants and food businesses.",
    longDescription: "SafeBite revolutionizes food safety management by combining IoT sensors, machine learning, and real-time analytics to prevent foodborne illnesses and ensure compliance with health regulations.",
    image: "https://placehold.co/600x400/365CCE/FFFFFF?text=SafeBite",
    technologies: ["React", "Node.js", "MongoDB", "AWS", "IoT", "Machine Learning"],
    category: "Full Stack",
    status: "In Development",
    github: "https://github.com/adityashenvi/safebite",
    demo: "https://safebite-demo.vercel.app",
    features: [
      "Real-time temperature monitoring",
      "AI-powered risk assessment",
      "Compliance dashboard",
      "Mobile app for staff",
      "Automated alerts and notifications"
    ],
    stats: {
      stars: 45,
      forks: 12,
      commits: 234
    }
  },
  {
    id: "ai-terminal",
    title: "AI Terminal Tool",
    description: "An intelligent terminal interface powered by Google Gemini AI that helps developers with command suggestions, code generation, and system administration.",
    longDescription: "This AI-powered terminal tool integrates with Google Gemini to provide intelligent command suggestions, automated script generation, and real-time help for developers and system administrators.",
    image: "https://placehold.co/600x400/365CCE/FFFFFF?text=AI+Terminal",
    technologies: ["React", "TypeScript", "Google Gemini API", "Electron", "Node.js"],
    category: "AI/ML",
    status: "Completed",
    github: "https://github.com/adityashenvi/ai-terminal",
    demo: "https://ai-terminal.vercel.app",
    features: [
      "Natural language command translation",
      "Code generation and explanation",
      "System monitoring and diagnostics",
      "Custom command aliases",
      "Multi-platform support"
    ],
    stats: {
      stars: 128,
      forks: 34,
      commits: 156
    }
  },
  {
    id: "cloud-cost-optimizer",
    title: "Multi-Cloud Cost Optimizer",
    description: "A comprehensive tool for comparing and optimizing costs across AWS, Azure, and Google Cloud Platform with real-time recommendations.",
    longDescription: "This tool helps organizations optimize their cloud spending by providing detailed cost analysis, usage patterns, and actionable recommendations across multiple cloud providers.",
    image: "https://placehold.co/600x400/365CCE/FFFFFF?text=Cloud+Optimizer",
    technologies: ["Python", "React", "AWS SDK", "Azure SDK", "GCP SDK", "Docker"],
    category: "DevOps",
    status: "Completed",
    github: "https://github.com/adityashenvi/cloud-cost-optimizer",
    demo: "https://cloud-optimizer.vercel.app",
    features: [
      "Multi-cloud cost comparison",
      "Usage analytics and trends",
      "Automated recommendations",
      "Budget alerts and notifications",
      "Resource optimization suggestions"
    ],
    stats: {
      stars: 89,
      forks: 23,
      commits: 198
    }
  }
];

interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string;
  html_url: string;
  homepage: string;
  stargazers_count: number;
  forks_count: number;
  language: string;
  updated_at: string;
  created_at: string;
  topics: string[];
  size: number;
  open_issues_count: number;
}

const CATEGORIES = ["All", "Full Stack", "AI/ML", "DevOps", "Mobile", "Web", "Tools"];

const ProjectsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [selectedProject, setSelectedProject] = useState<typeof PROJECTS[0] | null>(null);
  const [githubRepos, setGithubRepos] = useState<GitHubRepo[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showGithubRepos, setShowGithubRepos] = useState(false);
  const { toast } = useToast();

  // Fetch GitHub repositories
  useEffect(() => {
    const fetchGithubRepos = async () => {
      try {
        const response = await fetch('https://api.github.com/users/adityashenvi/repos?sort=updated&per_page=50');
        if (response.ok) {
          const repos = await response.json();
          // Filter out forks and private repos, sort by stars
          const publicRepos = repos
            .filter((repo: GitHubRepo) => !repo.fork && repo.description)
            .sort((a: GitHubRepo, b: GitHubRepo) => b.stargazers_count - a.stargazers_count);
          setGithubRepos(publicRepos);
        }
      } catch (error) {
        console.error('Failed to fetch GitHub repos:', error);
        toast({
          title: "GitHub API Error",
          description: "Failed to load GitHub repositories. Showing featured projects only.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    fetchGithubRepos();
  }, [toast]);

  const filteredProjects = selectedCategory === "All"
    ? PROJECTS
    : PROJECTS.filter(project => project.category === selectedCategory);

  const filteredGithubRepos = githubRepos.filter(repo =>
    repo.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    repo.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    repo.topics.some(topic => topic.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      JavaScript: '#f1e05a',
      TypeScript: '#2b7489',
      Python: '#3572A5',
      Java: '#b07219',
      'C++': '#f34b7d',
      Go: '#00ADD8',
      Rust: '#dea584',
      PHP: '#4F5D95',
      Ruby: '#701516',
      Swift: '#ffac45',
      Kotlin: '#F18E33',
      Dart: '#00B4AB',
      HTML: '#e34c26',
      CSS: '#1572B6',
      Shell: '#89e051'
    };
    return colors[language] || '#6b7280';
  };

  return (
    <div className="min-h-screen bg-background">
      <SimpleParticles />
      <Navbar />

      <div className="container py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-6xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              <span className="text-gradient">My Projects</span>
            </h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
              A collection of projects showcasing my expertise in full-stack development,
              cloud engineering, and AI/ML technologies.
            </p>
          </div>

          {/* Tabs for Featured vs GitHub */}
          <Tabs defaultValue="featured" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="featured">Featured Projects</TabsTrigger>
              <TabsTrigger value="github">GitHub Repositories</TabsTrigger>
            </TabsList>

            <TabsContent value="featured" className="space-y-8">
              {/* Category Filter */}
              <div className="flex flex-wrap justify-center gap-2">
                {CATEGORIES.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    onClick={() => setSelectedCategory(category)}
                    className="rounded-full"
                  >
                    {category}
                  </Button>
                ))}
              </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="neo-card h-full hover:shadow-lg transition-all duration-300 cursor-pointer"
                      onClick={() => setSelectedProject(project)}>
                  <div className="relative h-48 overflow-hidden rounded-t-lg">
                    <img
                      src={project.image}
                      alt={project.title}
                      className="w-full h-full object-cover transition-transform hover:scale-105 duration-500"
                    />
                    <div className="absolute top-2 right-2">
                      <Badge variant={project.status === "Completed" ? "default" : "secondary"}>
                        {project.status}
                      </Badge>
                    </div>
                  </div>

                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <CardTitle className="text-lg">{project.title}</CardTitle>
                      <Badge variant="outline">{project.category}</Badge>
                    </div>
                  </CardHeader>

                  <CardContent>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                      {project.description}
                    </p>

                    <div className="flex flex-wrap gap-1 mb-4">
                      {project.technologies.slice(0, 3).map((tech, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tech}
                        </Badge>
                      ))}
                      {project.technologies.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{project.technologies.length - 3}
                        </Badge>
                      )}
                    </div>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {project.stats.stars}
                        </div>
                        <div className="flex items-center gap-1">
                          <GitBranch className="h-3 w-3" />
                          {project.stats.forks}
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button variant="ghost" size="sm" asChild>
                          <a href={project.github} target="_blank" rel="noopener noreferrer">
                            <Github className="h-3 w-3" />
                          </a>
                        </Button>
                        <Button variant="ghost" size="sm" asChild>
                          <a href={project.demo} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
            </TabsContent>

            <TabsContent value="github" className="space-y-8">
              {/* Search Bar */}
              <div className="max-w-md mx-auto">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search repositories..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* GitHub Repositories */}
              {loading ? (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading GitHub repositories...</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredGithubRepos.map((repo, index) => (
                    <motion.div
                      key={repo.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                    >
                      <Card className="neo-card h-full hover:shadow-lg transition-all duration-300">
                        <CardHeader>
                          <div className="flex justify-between items-start">
                            <CardTitle className="text-lg flex items-center gap-2">
                              <Code className="h-5 w-5 text-primary" />
                              {repo.name}
                            </CardTitle>
                            {repo.language && (
                              <Badge
                                variant="outline"
                                className="text-xs"
                                style={{
                                  borderColor: getLanguageColor(repo.language),
                                  color: getLanguageColor(repo.language)
                                }}
                              >
                                {repo.language}
                              </Badge>
                            )}
                          </div>
                        </CardHeader>

                        <CardContent>
                          <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                            {repo.description || "No description available"}
                          </p>

                          {repo.topics.length > 0 && (
                            <div className="flex flex-wrap gap-1 mb-4">
                              {repo.topics.slice(0, 3).map((topic, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {topic}
                                </Badge>
                              ))}
                              {repo.topics.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{repo.topics.length - 3}
                                </Badge>
                              )}
                            </div>
                          )}

                          <div className="flex justify-between items-center mb-4">
                            <div className="flex items-center gap-3 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Star className="h-3 w-3" />
                                {repo.stargazers_count}
                              </div>
                              <div className="flex items-center gap-1">
                                <GitBranch className="h-3 w-3" />
                                {repo.forks_count}
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {formatDate(repo.updated_at)}
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" asChild className="flex-1">
                              <a href={repo.html_url} target="_blank" rel="noopener noreferrer">
                                <Github className="mr-2 h-3 w-3" />
                                Code
                              </a>
                            </Button>
                            {repo.homepage && (
                              <Button variant="outline" size="sm" asChild className="flex-1">
                                <a href={repo.homepage} target="_blank" rel="noopener noreferrer">
                                  <ExternalLink className="mr-2 h-3 w-3" />
                                  Demo
                                </a>
                              </Button>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              )}

              {!loading && filteredGithubRepos.length === 0 && (
                <div className="text-center py-12">
                  <Code className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No repositories found</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search terms or check back later.
                  </p>
                </div>
              )}
            </TabsContent>
          </Tabs>

          {/* Project Details Modal */}
          {selectedProject && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={() => setSelectedProject(null)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                className="bg-background rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="relative">
                  <img
                    src={selectedProject.image}
                    alt={selectedProject.title}
                    className="w-full h-64 object-cover"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 bg-background/80"
                    onClick={() => setSelectedProject(null)}
                  >
                    ✕
                  </Button>
                </div>

                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h2 className="text-2xl font-bold mb-2">{selectedProject.title}</h2>
                      <div className="flex gap-2">
                        <Badge variant="outline">{selectedProject.category}</Badge>
                        <Badge variant={selectedProject.status === "Completed" ? "default" : "secondary"}>
                          {selectedProject.status}
                        </Badge>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button variant="outline" asChild>
                        <a href={selectedProject.github} target="_blank" rel="noopener noreferrer">
                          <Github className="mr-2 h-4 w-4" />
                          GitHub
                        </a>
                      </Button>
                      <Button asChild>
                        <a href={selectedProject.demo} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="mr-2 h-4 w-4" />
                          Live Demo
                        </a>
                      </Button>
                    </div>
                  </div>

                  <p className="text-muted-foreground mb-6">
                    {selectedProject.longDescription}
                  </p>

                  <Tabs defaultValue="features" className="w-full">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="features">Features</TabsTrigger>
                      <TabsTrigger value="tech">Technologies</TabsTrigger>
                      <TabsTrigger value="stats">Statistics</TabsTrigger>
                    </TabsList>

                    <TabsContent value="features" className="space-y-2">
                      {selectedProject.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-primary" />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </TabsContent>

                    <TabsContent value="tech" className="space-y-2">
                      <div className="flex flex-wrap gap-2">
                        {selectedProject.technologies.map((tech, index) => (
                          <Badge key={index} variant="outline">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </TabsContent>

                    <TabsContent value="stats" className="space-y-4">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Star className="h-4 w-4 text-yellow-500" />
                            <span className="text-2xl font-bold">{selectedProject.stats.stars}</span>
                          </div>
                          <p className="text-sm text-muted-foreground">Stars</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <GitBranch className="h-4 w-4 text-blue-500" />
                            <span className="text-2xl font-bold">{selectedProject.stats.forks}</span>
                          </div>
                          <p className="text-sm text-muted-foreground">Forks</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center gap-1 mb-1">
                            <Code className="h-4 w-4 text-green-500" />
                            <span className="text-2xl font-bold">{selectedProject.stats.commits}</span>
                          </div>
                          <p className="text-sm text-muted-foreground">Commits</p>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </motion.div>
            </motion.div>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default ProjectsPage;
