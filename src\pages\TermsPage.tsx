
import { Navbar } from "@/components/Navbar";

const TermsPage = () => {
  return (
    <div className="min-h-screen">
      <Navbar />
      <div className="container py-20">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-4 tracking-tight">
              <span className="text-gradient bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Terms & Conditions
              </span>
            </h1>
            <p className="text-muted-foreground text-lg">
              Last updated: April 13, 2025
            </p>
          </div>

          <div className="blog-content prose prose-lg dark:prose-invert max-w-none">
            <div className="bg-muted/30 p-6 rounded-lg mb-8">
              <p className="text-muted-foreground mb-0 text-center font-medium">
                Please read these terms carefully before using our website and services.
              </p>
            </div>
          <p>
            Welcome to <PERSON><PERSON><PERSON>'s personal portfolio and developer tools website.
            By accessing or using this website, you agree to be bound by these Terms and Conditions.
          </p>

          <h2>1. Acceptance of Terms</h2>
          <p>
            By accessing and using this website, you accept and agree to be bound by the terms and provisions of this agreement.
            If you do not agree to abide by the terms and conditions, please do not use this website.
          </p>

          <h2>2. Use License</h2>
          <p>
            Permission is granted to temporarily view the materials on Aditya Shenvi's website for personal,
            non-commercial transitory use only. This is the grant of a license, not a transfer of title, and under this license you may not:
          </p>
          <ul>
            <li>Modify or copy the materials;</li>
            <li>Use the materials for any commercial purpose or for any public display;</li>
            <li>Attempt to reverse engineer any software contained on the website;</li>
            <li>Remove any copyright or other proprietary notations from the materials; or</li>
            <li>Transfer the materials to another person or "mirror" the materials on any other server.</li>
          </ul>

          <h2>3. User Accounts</h2>
          <p>
            When you create an account with us, you guarantee that the information you provide is accurate, complete,
            and current at all times. You are responsible for maintaining the confidentiality of your account and password.
          </p>

          <h2>4. Intellectual Property</h2>
          <p>
            The website and its original content, features, and functionality are owned by Aditya Shenvi and are protected
            by international copyright, trademark, patent, trade secret, and other intellectual property laws.
          </p>

          <h2>5. Termination</h2>
          <p>
            We may terminate or suspend your account and bar access to the website immediately, without prior notice or liability,
            under our sole discretion, for any reason whatsoever and without limitation.
          </p>

          <h2>6. Limitation of Liability</h2>
          <p>
            In no event shall Aditya Shenvi be liable for any damages arising out of the use or inability to use the materials
            on the website, even if Aditya Shenvi or an authorized representative has been notified orally or in writing of the
            possibility of such damage.
          </p>

          <h2>7. Changes to Terms</h2>
          <p>
            Aditya Shenvi reserves the right to modify these terms of service at any time. We will notify users of any changes
            by updating the date at the bottom of this page.
          </p>

          <h2>8. Governing Law</h2>
          <p>
            These terms and conditions are governed by and construed in accordance with the laws, and you irrevocably submit to
            the exclusive jurisdiction of the courts in that location.
          </p>

          </div>
        </div>
      </div>
    </div>
  );
};

export default TermsPage;
