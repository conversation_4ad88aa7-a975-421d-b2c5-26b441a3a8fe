import { initializeApp } from "firebase/app";
import { getAnalytics, logEvent } from "firebase/analytics";
import { getFirestore, collection, getDocs, addDoc, updateDoc, deleteDoc, doc, getDoc, query, where, orderBy, limit } from "firebase/firestore";
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, onAuthStateChanged, User } from "firebase/auth";
import { getStorage, ref, uploadBytes, getDownloadURL, deleteObject } from "firebase/storage";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC9dhOcTcOL4bi0rUIKrapPHBzWz49JSkM",
  authDomain: "adityashenvi-portfoliowebsite.firebaseapp.com",
  projectId: "adityashenvi-portfoliowebsite",
  storageBucket: "adityashenvi-portfoliowebsite.firebasestorage.app",
  messagingSenderId: "361489847943",
  appId: "1:361489847943:web:64e58aaeb0e904be658a22",
  measurementId: "G-NGTWQ99F4R"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);
const db = getFirestore(app);
const auth = getAuth(app);
const storage = getStorage(app);

// Analytics functions
export const trackPageView = (pageName: string) => {
  logEvent(analytics, 'page_view', {
    page_title: pageName,
    page_location: window.location.href,
    page_path: window.location.pathname
  });
};

export const trackEvent = (eventName: string, params?: Record<string, any>) => {
  logEvent(analytics, eventName, params);
};

// Auth functions
export const loginUser = (email: string, password: string) => {
  return signInWithEmailAndPassword(auth, email, password);
};

export const registerUser = (email: string, password: string) => {
  return createUserWithEmailAndPassword(auth, email, password);
};

export const logoutUser = () => {
  return signOut(auth);
};

export const getCurrentUser = (): Promise<User | null> => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe();
      resolve(user);
    });
  });
};

// Firestore functions
export const getBlogPosts = async () => {
  const postsCollection = collection(db, 'blog_posts');
  const postsQuery = query(postsCollection, orderBy('createdAt', 'desc'));
  const snapshot = await getDocs(postsQuery);
  return snapshot.docs.map(doc => ({
    id: doc.id,
    ...doc.data()
  }));
};

export const getBlogPost = async (id: string) => {
  const docRef = doc(db, 'blog_posts', id);
  const docSnap = await getDoc(docRef);
  
  if (docSnap.exists()) {
    return {
      id: docSnap.id,
      ...docSnap.data()
    };
  } else {
    return null;
  }
};

export const createBlogPost = async (postData: any) => {
  const postsCollection = collection(db, 'blog_posts');
  return addDoc(postsCollection, {
    ...postData,
    createdAt: new Date(),
    updatedAt: new Date()
  });
};

export const updateBlogPost = async (id: string, postData: any) => {
  const docRef = doc(db, 'blog_posts', id);
  return updateDoc(docRef, {
    ...postData,
    updatedAt: new Date()
  });
};

export const deleteBlogPost = async (id: string) => {
  const docRef = doc(db, 'blog_posts', id);
  return deleteDoc(docRef);
};

// Storage functions
export const uploadFile = async (file: File, path: string) => {
  const storageRef = ref(storage, path);
  await uploadBytes(storageRef, file);
  return getDownloadURL(storageRef);
};

export const deleteFile = async (path: string) => {
  const storageRef = ref(storage, path);
  return deleteObject(storageRef);
};

export { app, analytics, db, auth, storage };
