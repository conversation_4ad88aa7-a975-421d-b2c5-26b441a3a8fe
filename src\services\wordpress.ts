// WordPress REST API integration service
interface WordPressConfig {
  baseUrl: string;
  username?: string;
  password?: string;
  token?: string;
}

interface WordPressPost {
  id: number;
  title: {
    rendered: string;
  };
  content: {
    rendered: string;
  };
  excerpt: {
    rendered: string;
  };
  date: string;
  modified: string;
  slug: string;
  status: 'publish' | 'draft' | 'private';
  author: number;
  featured_media: number;
  categories: number[];
  tags: number[];
  meta: Record<string, any>;
}

interface WordPressUser {
  id: number;
  username: string;
  name: string;
  email: string;
  roles: string[];
  capabilities: Record<string, boolean>;
}

interface WordPressAuthResponse {
  token: string;
  user_email: string;
  user_nicename: string;
  user_display_name: string;
}

class WordPressService {
  private config: WordPressConfig;
  private authToken: string | null = null;

  constructor(config: WordPressConfig) {
    this.config = config;
    this.authToken = localStorage.getItem('wp_auth_token');
  }

  // Authentication methods
  async authenticate(username: string, password: string): Promise<WordPressAuthResponse> {
    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/jwt-auth/v1/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      });

      if (!response.ok) {
        throw new Error('Authentication failed');
      }

      const data = await response.json();
      this.authToken = data.token;
      localStorage.setItem('wp_auth_token', data.token);
      
      return data;
    } catch (error) {
      console.error('WordPress authentication error:', error);
      throw error;
    }
  }

  async validateToken(): Promise<boolean> {
    if (!this.authToken) return false;

    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/jwt-auth/v1/token/validate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  logout(): void {
    this.authToken = null;
    localStorage.removeItem('wp_auth_token');
  }

  // Posts management
  async getPosts(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    categories?: number[];
    tags?: number[];
    status?: string;
  }): Promise<WordPressPost[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.per_page) queryParams.append('per_page', params.per_page.toString());
      if (params?.search) queryParams.append('search', params.search);
      if (params?.categories) queryParams.append('categories', params.categories.join(','));
      if (params?.tags) queryParams.append('tags', params.tags.join(','));
      if (params?.status) queryParams.append('status', params.status);

      const url = `${this.config.baseUrl}/wp-json/wp/v2/posts?${queryParams.toString()}`;
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      const response = await fetch(url, { headers });

      if (!response.ok) {
        throw new Error(`Failed to fetch posts: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching posts:', error);
      throw error;
    }
  }

  async getPost(id: number): Promise<WordPressPost> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/posts/${id}`, {
        headers,
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch post: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching post:', error);
      throw error;
    }
  }

  async createPost(post: Partial<WordPressPost>): Promise<WordPressPost> {
    if (!this.authToken) {
      throw new Error('Authentication required');
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/posts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(post),
      });

      if (!response.ok) {
        throw new Error(`Failed to create post: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating post:', error);
      throw error;
    }
  }

  async updatePost(id: number, post: Partial<WordPressPost>): Promise<WordPressPost> {
    if (!this.authToken) {
      throw new Error('Authentication required');
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/posts/${id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: JSON.stringify(post),
      });

      if (!response.ok) {
        throw new Error(`Failed to update post: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating post:', error);
      throw error;
    }
  }

  async deletePost(id: number): Promise<boolean> {
    if (!this.authToken) {
      throw new Error('Authentication required');
    }

    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/posts/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Error deleting post:', error);
      throw error;
    }
  }

  // User management
  async getCurrentUser(): Promise<WordPressUser | null> {
    if (!this.authToken) return null;

    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/users/me`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
      });

      if (!response.ok) {
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching current user:', error);
      return null;
    }
  }

  // Media management
  async uploadMedia(file: File): Promise<any> {
    if (!this.authToken) {
      throw new Error('Authentication required');
    }

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`${this.config.baseUrl}/wp-json/wp/v2/media`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload media: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading media:', error);
      throw error;
    }
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.authToken;
  }

  getAuthToken(): string | null {
    return this.authToken;
  }
}

// Default WordPress configuration
const defaultConfig: WordPressConfig = {
  baseUrl: process.env.REACT_APP_WORDPRESS_URL || 'https://your-wordpress-site.com',
};

// Export singleton instance
export const wordpressService = new WordPressService(defaultConfig);
export type { WordPressPost, WordPressUser, WordPressAuthResponse };
export { WordPressService };
