/* AMP-specific styles */

/* AMP mode indicator */
.amp-mode {
  /* Add a subtle indicator for AMP mode (for development) */
  position: relative;
}

/* Optimized animations for AMP */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInFromLeft {
  from { opacity: 0; transform: translateX(-20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes slideInFromRight {
  from { opacity: 0; transform: translateX(20px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

/* Animation classes */
.amp-fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

.amp-slide-in-left {
  animation: slideInFromLeft 0.5s ease-in-out forwards;
}

.amp-slide-in-right {
  animation: slideInFromRight 0.5s ease-in-out forwards;
}

.amp-bounce {
  animation: bounce 2s infinite;
}

/* Delay classes */
.amp-delay-1 {
  animation-delay: 0.1s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.amp-delay-2 {
  animation-delay: 0.2s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.amp-delay-3 {
  animation-delay: 0.3s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.amp-delay-4 {
  animation-delay: 0.4s;
  opacity: 0;
  animation-fill-mode: forwards;
}

.amp-delay-5 {
  animation-delay: 0.5s;
  opacity: 0;
  animation-fill-mode: forwards;
}

/* Optimized image placeholders */
.amp-img-placeholder {
  background-color: #f0f0f0;
  position: relative;
  overflow: hidden;
}

.amp-img-placeholder::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Optimized layout for AMP */
.amp-layout-container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Optimized responsive grid for AMP */
.amp-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

/* Optimized card for AMP */
.amp-card {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.amp-card:hover {
  transform: translateY(-5px);
}

/* Optimized button for AMP */
.amp-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  transition: background-color 0.3s ease;
}

/* Optimized text for AMP */
.amp-text-gradient {
  background: linear-gradient(to right, #3b82f6, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

/* Optimized glass effect for AMP */
.amp-glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
}
