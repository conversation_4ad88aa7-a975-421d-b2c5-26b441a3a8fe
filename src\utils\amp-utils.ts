/**
 * Utility functions for AMP compatibility
 */

/**
 * Checks if the current page is being viewed as an AMP page
 */
export const isAmpPage = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  return (
    window.location.search.includes('amp=1') || 
    document.documentElement.hasAttribute('amp') ||
    window.location.pathname.includes('/amp/')
  );
};

/**
 * Generates an AMP-compatible version of a URL
 */
export const getAmpUrl = (url: string): string => {
  // Remove any existing query parameters
  const baseUrl = url.split('?')[0];
  
  // Add AMP parameter
  return `${baseUrl}?amp=1`;
};

/**
 * Generates a canonical URL for an AMP page
 */
export const getCanonicalUrl = (ampUrl: string): string => {
  // Remove AMP parameter
  return ampUrl.replace('?amp=1', '').replace('/amp/', '/');
};

/**
 * Optimizes an image URL for AMP
 * This can be used to point to a smaller/optimized version of images for AMP pages
 */
export const getAmpImageUrl = (imageUrl: string, width: number = 600): string => {
  // For demonstration, we're just adding a width parameter
  // In a real implementation, you might use an image CDN or resizing service
  if (imageUrl.includes('?')) {
    return `${imageUrl}&width=${width}`;
  }
  return `${imageUrl}?width=${width}`;
};

/**
 * Creates AMP-compatible inline CSS
 * AMP requires all CSS to be inlined and under 75KB
 */
export const createAmpStyles = (styles: string): string => {
  // Remove comments and minimize
  const minifiedStyles = styles
    .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
    .replace(/\s+/g, ' ')             // Collapse whitespace
    .replace(/\s*([{}:;,])\s*/g, '$1'); // Remove spaces around special chars
  
  return minifiedStyles;
};

/**
 * Sanitizes HTML for AMP compatibility
 * Removes forbidden tags and attributes
 */
export const sanitizeHtmlForAmp = (html: string): string => {
  // This is a simplified version - a real implementation would be more comprehensive
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '') // Remove iframe tags
    .replace(/style="[^"]*"/gi, '')                                     // Remove inline styles
    .replace(/onclick="[^"]*"/gi, '')                                   // Remove onclick attributes
    .replace(/on\w+="[^"]*"/gi, '');                                    // Remove all event handlers
};

/**
 * Converts a regular image to an AMP-compatible image
 */
export const convertToAmpImage = (imgSrc: string, width: number, height: number, layout: string = 'responsive'): string => {
  return `<amp-img src="${imgSrc}" width="${width}" height="${height}" layout="${layout}"></amp-img>`;
};

/**
 * Converts a YouTube embed to an AMP-compatible embed
 */
export const convertToAmpYoutube = (videoId: string, width: number = 560, height: number = 315): string => {
  return `<amp-youtube data-videoid="${videoId}" layout="responsive" width="${width}" height="${height}"></amp-youtube>`;
};
