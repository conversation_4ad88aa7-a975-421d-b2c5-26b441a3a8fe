// Performance optimization utilities for animations and rendering

interface PerformanceMetrics {
  fps: number;
  isLowPerformance: boolean;
  deviceType: 'mobile' | 'tablet' | 'desktop';
  reducedMotion: boolean;
  gpuAcceleration: boolean;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics;
  private frameCount = 0;
  private lastTime = performance.now();
  private animationId: number | null = null;
  private callbacks: ((metrics: PerformanceMetrics) => void)[] = [];

  private constructor() {
    this.metrics = {
      fps: 60,
      isLowPerformance: false,
      deviceType: this.detectDeviceType(),
      reducedMotion: this.detectReducedMotion(),
      gpuAcceleration: this.detectGPUAcceleration(),
    };

    this.startMonitoring();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private detectDeviceType(): 'mobile' | 'tablet' | 'desktop' {
    const userAgent = navigator.userAgent;
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)|Android(?=.*\bTablet\b)/i.test(userAgent);
    
    if (isMobile && !isTablet) return 'mobile';
    if (isTablet) return 'tablet';
    return 'desktop';
  }

  private detectReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  private detectGPUAcceleration(): boolean {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  }

  private startMonitoring() {
    const measureFPS = () => {
      this.frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - this.lastTime >= 1000) {
        const currentFPS = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
        this.metrics.fps = currentFPS;
        this.metrics.isLowPerformance = currentFPS < 30;
        
        // Notify callbacks
        this.callbacks.forEach(callback => callback(this.metrics));
        
        this.frameCount = 0;
        this.lastTime = currentTime;
      }
      
      this.animationId = requestAnimationFrame(measureFPS);
    };

    this.animationId = requestAnimationFrame(measureFPS);
  }

  subscribe(callback: (metrics: PerformanceMetrics) => void) {
    this.callbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.callbacks.indexOf(callback);
      if (index > -1) {
        this.callbacks.splice(index, 1);
      }
    };
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    this.callbacks = [];
  }
}

// Animation optimization utilities
export const AnimationOptimizer = {
  // Get optimized animation variants based on performance
  getOptimizedVariants: (baseVariants: any, metrics?: PerformanceMetrics) => {
    const currentMetrics = metrics || PerformanceMonitor.getInstance().getMetrics();
    
    if (currentMetrics.reducedMotion) {
      return {
        ...baseVariants,
        animate: { ...baseVariants.animate, transition: { duration: 0 } },
      };
    }

    if (currentMetrics.isLowPerformance || currentMetrics.deviceType === 'mobile') {
      return {
        ...baseVariants,
        animate: {
          ...baseVariants.animate,
          transition: {
            ...baseVariants.animate?.transition,
            duration: (baseVariants.animate?.transition?.duration || 0.5) * 0.5,
            ease: 'easeOut',
          },
        },
      };
    }

    return baseVariants;
  },

  // Throttle animation updates
  throttleAnimation: (callback: () => void, delay = 16) => {
    let lastCall = 0;
    return () => {
      const now = performance.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        callback();
      }
    };
  },

  // Debounce resize events
  debounceResize: (callback: () => void, delay = 250) => {
    let timeoutId: NodeJS.Timeout;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(callback, delay);
    };
  },

  // GPU-accelerated CSS properties
  getGPUAcceleratedStyles: (metrics?: PerformanceMetrics) => {
    const currentMetrics = metrics || PerformanceMonitor.getInstance().getMetrics();
    
    if (!currentMetrics.gpuAcceleration) {
      return {};
    }

    return {
      transform: 'translateZ(0)',
      willChange: 'transform, opacity',
      backfaceVisibility: 'hidden' as const,
      perspective: 1000,
    };
  },

  // Optimize scroll animations
  optimizeScrollAnimation: (element: HTMLElement, callback: (progress: number) => void) => {
    let ticking = false;

    const updateAnimation = () => {
      const rect = element.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const progress = Math.max(0, Math.min(1, (windowHeight - rect.top) / (windowHeight + rect.height)));
      
      callback(progress);
      ticking = false;
    };

    const onScroll = () => {
      if (!ticking) {
        requestAnimationFrame(updateAnimation);
        ticking = true;
      }
    };

    window.addEventListener('scroll', onScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', onScroll);
    };
  },
};

// React hook for performance monitoring
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>(() => 
    PerformanceMonitor.getInstance().getMetrics()
  );

  useEffect(() => {
    const monitor = PerformanceMonitor.getInstance();
    const unsubscribe = monitor.subscribe(setMetrics);

    return unsubscribe;
  }, []);

  return metrics;
};

// React hook for optimized animations
export const useOptimizedAnimation = (baseVariants: any) => {
  const metrics = usePerformanceMonitor();
  
  return useMemo(() => 
    AnimationOptimizer.getOptimizedVariants(baseVariants, metrics),
    [baseVariants, metrics.isLowPerformance, metrics.reducedMotion, metrics.deviceType]
  );
};

// React hook for throttled callbacks
export const useThrottledCallback = (callback: () => void, delay = 16) => {
  const throttledCallback = useMemo(
    () => AnimationOptimizer.throttleAnimation(callback, delay),
    [callback, delay]
  );

  return throttledCallback;
};

// React hook for debounced callbacks
export const useDebouncedCallback = (callback: () => void, delay = 250) => {
  const debouncedCallback = useMemo(
    () => AnimationOptimizer.debounceResize(callback, delay),
    [callback, delay]
  );

  return debouncedCallback;
};

// Performance budget checker
export const PerformanceBudget = {
  // Check if animation should be enabled
  shouldAnimate: (metrics?: PerformanceMetrics) => {
    const currentMetrics = metrics || PerformanceMonitor.getInstance().getMetrics();
    return !currentMetrics.reducedMotion && !currentMetrics.isLowPerformance;
  },

  // Check if complex animations should be enabled
  shouldUseComplexAnimations: (metrics?: PerformanceMetrics) => {
    const currentMetrics = metrics || PerformanceMonitor.getInstance().getMetrics();
    return currentMetrics.fps > 45 && 
           currentMetrics.deviceType === 'desktop' && 
           !currentMetrics.reducedMotion;
  },

  // Check if 3D transforms should be used
  shouldUse3D: (metrics?: PerformanceMetrics) => {
    const currentMetrics = metrics || PerformanceMonitor.getInstance().getMetrics();
    return currentMetrics.gpuAcceleration && 
           currentMetrics.fps > 30 && 
           !currentMetrics.reducedMotion;
  },
};

// Export the performance monitor instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Import statements for React hooks
import { useState, useEffect, useMemo } from 'react';
